import React, {
  forwardRef,
  useCallback,
  useImperative<PERSON>andle,
  useRef,
  useState,
} from 'react';
import { ScrollView, TouchableOpacity } from 'react-native';
import {
  BlockView,
  CModal,
  ColorsV2,
  FastImage,
  getTextWithLocale,
  Icon,
  PriceButton,
} from '@btaskee/design-system';
import { debounce } from 'lodash-es';

import { useAppNavigation, useI18n } from '@hooks';
import { sofaHeader12Seat, sofaHeader34Seat, sofaHeader56Seat } from '@images';
import { RouteName } from '@navigation/RouteName';
import { usePostTaskStore } from '@stores';

import { NumberSpinner } from '../NumberSpinner';
import { styles } from './styles';

// Constants
const DEBOUNCE_TIME = 400;
const DEFAULT_QUANTITY_VALUE = 0;

// Types
interface SofaTabData {
  name: string;
  text: string;
  [key: string]: any;
}

interface CleaningData {
  name: string;
  text: string;
  type: {
    name: string;
    text: string;
    quantity: number;
  };
}

interface TypeData {
  name?: string;
  text?: string;
  quantity?: number;
  [key: string]: any;
}

interface ModalChangeQuantityProps {
  onChangeDetail: (data: any) => void;
  sofaTabData: SofaTabData;
}

interface ModalRef {
  open: () => void;
  close: () => void;
}

interface OpenParams {
  type: TypeData;
  indexType: number;
}

// Static data
const HEADER_IMAGE_TYPE: Record<string, any> = {
  '12seats': sofaHeader12Seat,
  '34seats': sofaHeader34Seat,
  '56seats': sofaHeader56Seat,
} as const;

export const ModalChangeQuantity = forwardRef<any, ModalChangeQuantityProps>(
  ({ onChangeDetail, sofaTabData }, ref) => {
    const navigation = useAppNavigation();
    const { price } = usePostTaskStore();

    // State
    const [typeData, setTypeData] = useState<TypeData | undefined>();
    const [quantity, setQuantity] = useState(DEFAULT_QUANTITY_VALUE);

    // Refs
    const modalRef = useRef<ModalRef>(null);

    // Handlers
    const handleClose = useCallback(() => {
      modalRef.current?.close();
    }, []);

    const handleOpen = useCallback(({ type }: OpenParams) => {
      setTypeData(type);
      setQuantity(type.quantity || DEFAULT_QUANTITY_VALUE);
      modalRef.current?.open();
    }, []);

    const handleQuantityChange = useCallback(
      (newQuantity: number) => {
        if (!typeData || !sofaTabData || !typeData.name || !typeData.text)
          return;

        setQuantity(newQuantity);

        const updatedTypeData = {
          name: typeData.name,
          text: typeData.text,
          quantity: newQuantity,
        };

        onChangeDetail({
          name: sofaTabData.name,
          text: sofaTabData.text,
          type: updatedTypeData,
        });
      },
      [typeData, sofaTabData, onChangeDetail],
    );

    // Debounced version of quantity change handler
    const handleQuantityChangeDebounced = useCallback(
      debounce(handleQuantityChange, DEBOUNCE_TIME),
      [handleQuantityChange],
    );

    const handleNextStep = useCallback(() => {
      handleClose();
      navigation.navigate(RouteName.ChooseDateTime);
    }, [handleClose, navigation]);

    // Expose methods to parent component
    useImperativeHandle(
      ref,
      () => ({
        open: handleOpen,
        close: handleClose,
      }),
      [handleOpen, handleClose],
    );

    const renderHeaderImage = useCallback(
      (name?: string) => {
        if (!name) return null;

        const imageSource = HEADER_IMAGE_TYPE[name];

        return (
          <BlockView>
            {imageSource && (
              <FastImage
                resizeMode="cover"
                source={imageSource}
                style={styles.headerImage}
              />
            )}

            <TouchableOpacity
              testID="btnCloseModal"
              style={styles.iconCloseContainer}
              onPress={handleClose}>
              <Icon
                resizeMode="cover"
                name="icRemoveCircle"
                style={styles.iconClose}
                color={ColorsV2.neutralBackground}
              />
            </TouchableOpacity>
          </BlockView>
        );
      },
      [handleClose],
    );

    return (
      <CModal
        isShowHeader={false}
        ref={modalRef}
        contentContainerStyle={styles.contentContainerStyle}
        containerModal={{ paddingTop: 0 }}>
        <BlockView>
          {renderHeaderImage(typeData?.name)}

          <ScrollView
            scrollIndicatorInsets={{ right: 1 }}
            style={styles.contentContainer}>
            <NumberSpinner
              testID={typeData?.name || 'numberSpinner'}
              value={quantity}
              title={
                typeData?.text ? getTextWithLocale(typeData.text as any) : ''
              }
              onValueChange={handleQuantityChangeDebounced}
            />
          </ScrollView>

          <PriceButton
            testID="btnNextStep2"
            pricePostTask={price}
            onPress={handleNextStep}
          />
        </BlockView>
      </CModal>
    );
  },
);
