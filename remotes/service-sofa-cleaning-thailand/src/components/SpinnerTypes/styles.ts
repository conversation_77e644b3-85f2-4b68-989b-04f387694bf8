import { StyleSheet } from 'react-native';
import {
  ColorsV2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ontSizes,
  Spacing,
} from '@btaskee/design-system';

const { WIDTH } = DeviceHelper.WINDOW;

export const styles = StyleSheet.create({
  cardStoolsStyle: {
    padding: 0,
    margin: 0,
  },

  txtTitleChooseStool: {
    color: ColorsV2.neutral500,
  },
  txtCheckbox: {
    flex: 1,
    padding: 0,
    color: ColorsV2.neutral500,
    fontWeight: 'bold',
    fontSize: FontSizes.SIZE_14,
  },

  containerCheckBox: {
    paddingVertical: Spacing.SPACE_16,
    margin: 0,
    padding: 0,
    backgroundColor: ColorsV2.neutralWhite,
    borderWidth: 0,
    borderRadius: 0,
  },
  boxDescription: {
    marginTop: -Spacing.SPACE_16,
    paddingHorizontal: Spacing.SPACE_20,
    paddingBottom: Spacing.SPACE_20,
  },
  textDescription: {
    color: ColorsV2.neutral500,
    fontSize: 12,
  },
  cardStoolsSelected: {
    borderWidth: 1,
    borderColor: ColorsV2.orange500,
  },
  boxSpinner: {
    padding: Spacing.SPACE_12,
    paddingBottom: Spacing.SPACE_16,
    flexDirection: 'row',
    marginTop: -Spacing.SPACE_20,
  },
  boxItemSpinner: {
    paddingVertical: Spacing.SPACE_12,
  },
  itemImage: {
    width: WIDTH * 0.5,
    height: WIDTH * 0.5,
    borderRadius: 10,
  },
  boxQuantity: {
    justifyContent: 'flex-end',
    marginLeft: Spacing.SPACE_12,
  },
  inputContainerStyle: {
    borderBottomColor: ColorsV2.neutral100,
    borderBottomWidth: 1,
  },
  containerStyle: {
    paddingBottom: Spacing.SPACE_16,
    paddingHorizontal: Spacing.SPACE_12,
  },
  containerQuantity: {
    justifyContent: 'flex-end',
  },
});
