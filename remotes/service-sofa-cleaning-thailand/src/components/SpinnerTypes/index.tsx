import React, { useEffect, useState } from 'react';
import { Keyboard } from 'react-native';
import {
  BlockView,
  Card,
  ColorsV2,
  ConditionView,
  CText,
  CTextInput,
  FastImage,
  FontSizes,
  getTextWithLocale,
  Quantity,
  Spacing,
} from '@btaskee/design-system';
import { SOFA_TYPE_FROM } from '@constants';
import { CheckBox } from '@rneui/themed';
import { find } from 'lodash-es';

import { useAppNavigation, useI18n } from '@hooks';

import { styles } from './styles';

const SOFA_QUANTITY_DEFAULT = 1;

interface SpinnerTypesProps {
  data: any;
  onChangeValue: (value: any) => void;
  value: any;
  isUseSofaType: boolean;
  from: string;
  testID: string;
}

export const SpinnerTypes = ({
  data, // data to render UI
  value,
  onChangeValue,
  isUseSofaType,
  from,
}: SpinnerTypesProps) => {
  const { t } = useI18n();
  const navigation = useAppNavigation();

  const [isShowSpinner, setShowSpinner] = useState(false);
  const [note, setNote] = useState();
  const [dataChoose, setDataChoose] = useState();
  const disabled = !!(data.name === 'normal' && !isUseSofaType);

  const isUseName =
    [SOFA_TYPE_FROM.CURTAIN, SOFA_TYPE_FROM.SOFA].indexOf(from) >= 0;

  const _onChangeValue = (number = 0) => {
    if (number === 0) {
      // if quantity is 0, hide view and clear data
      return _setShowSpinner();
    }
    const newObj = { quantity: number, text: data?.text };
    if (isUseName) {
      newObj.name = data?.name;
    } else {
      newObj.type = data?.type;
    }

    if (note) {
      newObj.note = note;
    }
    onChangeValue(newObj);
  };

  const _setShowSpinner = () => {
    // AnimationHelpers.runLayoutAnimation();
    setShowSpinner(!isShowSpinner);
    // quantity default is 1
    const obj = { quantity: SOFA_QUANTITY_DEFAULT, text: data?.text };

    if (isUseName) {
      obj.name = data?.name;
    } else {
      obj.type = data?.type;
    }

    if (isShowSpinner) {
      // disable checkbox set quantity is 0
      obj.quantity = 0;
    }

    if (note) {
      obj.note = note;
    }
    onChangeValue && onChangeValue(obj);
  };

  useEffect(() => {
    const dataDefault = isUseName
      ? value?.find((item: any) => item?.name === data?.name)
      : value?.find((item: any) => item?.type === data?.type);

    if (dataDefault) {
      setDataChoose(dataDefault);
      if (dataDefault?.note) {
        setNote(dataDefault?.note);
      }
      if (dataDefault?.quantity > 0) {
        setShowSpinner(true);
      }
    }
    // Lỗi rerender liên tục khi chọn 2 item và nhập note
    // return () => {
    //   if (note) {
    //     onChangeValue && onChangeValue({ ...dataChoose, note: note });
    //   }
    // };
  }, [data, value]);

  const cardSelected = isShowSpinner ? styles.cardStoolsSelected : {};

  const _onChangeText = (value) => {
    setNote(value);
  };
  const _onEndEditText = () => {
    onChangeValue && onChangeValue({ ...dataChoose, note: note });
  };

  let testIdCB = `check_box_${data.type}_${from || ''}`;
  let testIdInput = `input_note_${data.type}_${from || ''}`;

  if (isUseName) {
    testIdCB = `check_box_${data.name}_${from || ''}`;
    testIdInput = `input_note_${data.name}_${from || ''}`;
  }

  if (!data || (data && data.length === 0)) return null;

  return (
    <BlockView style={{ paddingVertical: Spacing.SPACE_12 }}>
      <Card style={[styles.cardStoolsStyle, cardSelected]}>
        <BlockView>
          <CheckBox
            disabled={disabled}
            testID={testIdCB}
            iconRight={true}
            textStyle={[
              styles.txtCheckbox,
              disabled ? { color: ColorsV2.neutral500 } : {},
            ]}
            checked={isShowSpinner}
            onPress={_setShowSpinner}
            containerStyle={[styles.containerCheckBox]}
            checkedColor={ColorsV2.orange500}
            title={getTextWithLocale(data?.text)}
          />
          <ConditionView
            condition={data?.description}
            viewTrue={
              <BlockView style={styles.boxDescription}>
                <CText style={styles.textDescription}>
                  {getTextWithLocale(data?.description)}
                </CText>
              </BlockView>
            }
          />
        </BlockView>
        <ConditionView
          condition={isShowSpinner}
          viewTrue={
            <BlockView>
              <BlockView style={styles.boxSpinner}>
                <ConditionView
                  condition={data?.imageUrl}
                  viewTrue={
                    <FastImage
                      source={{ uri: data?.imageUrl }}
                      style={styles.itemImage}
                    />
                  }
                />
                <BlockView
                  flex
                  style={styles.boxQuantity}>
                  <Quantity
                    onChangeValue={_onChangeValue}
                    textTitleStyle={styles.txtTitleChooseStool}
                    title={t('QUANTITY')}
                    quantity={dataChoose?.quantity || SOFA_QUANTITY_DEFAULT}
                    sizeButton={24}
                    containerStyle={styles.containerQuantity}
                  />
                </BlockView>
              </BlockView>
              <CTextInput
                testID={testIdInput}
                value={note}
                label={null}
                numberOfLines={1}
                onChangeText={_onChangeText}
                onEndEditing={_onEndEditText}
                placeholder={t('NOTE_MATTRESS')}
                onSubmitEditing={() => {
                  Keyboard.dismiss();
                }}
                inputStyle={{
                  fontSize: FontSizes.SIZE_14,
                  color: ColorsV2.neutral500,
                }}
                inputContainerStyle={styles.inputContainerStyle}
                containerStyle={styles.containerStyle}
              />
            </BlockView>
          }
        />
      </Card>
    </BlockView>
  );
};
