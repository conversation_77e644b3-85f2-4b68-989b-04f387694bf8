/**
 * @Filename: TaskDetail/index.tsx
 * @Description: TaskDetail component for service-sofa-cleaning
 * @CreatedAt: 18/9/2020
 * @Author: DucAnh
 * @UpdatedAt: 1/12/2020
 * @UpdatedBy: HongKhanh, <PERSON><PERSON>
 **/

import React from 'react';
import {
  BlockView,
  Card,
  ConditionView,
  CText,
  Icon,
  WorkingTime,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';
import { usePostTaskStore } from '@stores';

import { DetailSofa } from './DetailSofa';
import { styles } from './styles';

/**
 * TaskDetail component displays task information and service details
 * for sofa cleaning services including sofa, curtain, mattress, and carpet cleaning
 */
export const TaskDetail: React.FC = () => {
  const { t } = useI18n();

  const { note, date, service, duration } = usePostTaskStore();

  return (
    <BlockView>
      <BlockView style={styles.panel}>
        <CText
          bold
          style={styles.txtPanel}>
          {t('TASK_INFO')}
        </CText>
      </BlockView>

      <Card style={styles.wrapWorkingTime}>
        <WorkingTime
          date={date}
          service={service}
          duration={duration}
        />

        <BlockView style={styles.wrapDetail}>
          <CText
            bold
            style={styles.subPanel}>
            {t('TASK_DETAIL')}
          </CText>

          <BlockView style={styles.group}>
            {/* SOFA DATA */}
            <DetailSofa />
          </BlockView>

          {/* NOTE SECTION */}
          <ConditionView
            condition={Boolean(note)}
            viewTrue={
              <BlockView style={styles.groupNote}>
                <Icon
                  name="icNote"
                  style={styles.iconSetting}
                />
                <CText style={styles.txtValue}>{note}</CText>
              </BlockView>
            }
          />
        </BlockView>
      </Card>
    </BlockView>
  );
};
