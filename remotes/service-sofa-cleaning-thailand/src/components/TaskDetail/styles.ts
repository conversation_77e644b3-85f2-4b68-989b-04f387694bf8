import { StyleSheet } from 'react-native';
import { Colors, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  panel: {
    marginTop: Spacing.SPACE_24,
    marginBottom: Spacing.SPACE_16,
    justifyContent: 'space-between',
  },

  txtPanel: {
    fontSize: 20,
  },

  subPanel: {
    marginBottom: Spacing.SPACE_04,
    color: Colors.BLACK,
    fontSize: 16,
  },

  group: {},

  groupNote: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.BORDER_COLOR,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 5,
  },

  wrapDetail: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.BORDER_COLOR,
  },

  wrapWorkingTime: {
    padding: 16,
  },

  iconSetting: {
    width: 24,
    height: 24,
    marginRight: 8,
  },

  txtValue: {
    width: '65%',
    textAlign: 'right',
  },
});
