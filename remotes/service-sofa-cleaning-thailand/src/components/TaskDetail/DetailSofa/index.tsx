import React from 'react';
import {
  <PERSON>View,
  CText,
  getTextWithLocale,
  Spacing,
} from '@btaskee/design-system';
import { SERVICE_SOFA_OPTION } from '@constants';

import { useI18n } from '@hooks';
import { usePostTaskStore } from '@stores';

import type {
  BedItem as BedItemType,
  CarpetItem,
  CarpetType,
  CurtainItem,
  CurtainType,
  SofaItem,
  SofaType,
} from '../../../types/task';
import { styles } from './styles';

// Interface for component props
interface RowItemProps {
  title: string;
  value: string;
}

interface ItemTypesProps {
  types?: (SofaType | CurtainType | CarpetType)[];
}

interface ServiceDetailItemProps {
  title: string;
  data: SofaItem[] | CurtainItem[] | CarpetItem[];
  typeLabel: string;
}

interface BedDetailsProps {
  data: BedItemType[];
}

// Helper function to safely get text
const getDisplayText = (text: string | any): string => {
  if (typeof text === 'string') {
    return text;
  }
  return getTextWithLocale(text);
};

// Type guard functions - Cách 1: Sử dụng type guards
const isCurtainItem = (item: any): item is CurtainItem => {
  return item && typeof item.serviceOption !== 'undefined';
};

const hasAdditionalSeat = (item: any): boolean => {
  return Boolean(item?.additionalSeat && item.additionalSeat > 0);
};

const hasCustomArea = (item: any): boolean => {
  return Boolean(item?.customArea && item?.quantityCustomArea);
};

// Sub-components
const RowItem: React.FC<RowItemProps> = ({ title, value }) => (
  <BlockView
    style={{ marginTop: Spacing.SPACE_04 }}
    right>
    <CText>
      <CText style={styles.txtTypeName}>{title}</CText>
      <CText
        bold
        style={styles.txtQuantity}>
        {value}
      </CText>
    </CText>
  </BlockView>
);

const ItemTypes: React.FC<ItemTypesProps> = ({ types }) => {
  if (!types || types.length === 0) {
    return null;
  }

  return (
    <>
      {types.map((type, index) => (
        <RowItem
          key={`itemType-${index}`}
          title={getDisplayText(type.text)}
          value={` x${type.quantity}`}
        />
      ))}
    </>
  );
};

const ServiceDetailItem: React.FC<ServiceDetailItemProps> = ({
  title,
  data,
  typeLabel,
}) => {
  const { t } = useI18n();

  const getServiceOption = (option: string): string | null => {
    const foundOption = SERVICE_SOFA_OPTION.find(
      (element) => element?.serviceOption === option,
    );
    return foundOption?.title ? t(foundOption.title) : null;
  };

  const renderServiceItem = (
    item: SofaItem | CurtainItem | CarpetItem,
    index: number,
  ) => {
    const isLastItem = index === data.length - 1;
    const marginBottom = isLastItem ? {} : { marginBottom: Spacing.SPACE_12 };

    // Handle text property that might be optional
    const itemText = item.text || '';

    return (
      <BlockView
        right
        key={`service-item-${index}`}
        style={marginBottom}>
        <CText
          bold
          testID="txtType"
          style={[styles.txtTypeName, { marginBottom: Spacing.SPACE_04 }]}>
          {getDisplayText(itemText)}
        </CText>

        {/* Cách 1: Sử dụng type guard function */}
        {isCurtainItem(item) && item.serviceOption && (
          <CText
            testID="serviceOption"
            style={[styles.txtTypeName, { marginBottom: Spacing.SPACE_04 }]}>
            {getServiceOption(item.serviceOption)}
          </CText>
        )}

        <ItemTypes types={item.type} />

        {/* Cách 2: Sử dụng utility function */}
        {hasAdditionalSeat(item) && (
          <RowItem
            title={t('SOFA_CLEANING_TYPE_MORE')}
            value={` x${(item as SofaItem).additionalSeat}`}
          />
        )}

        {/* Cách 3: Sử dụng utility function kết hợp type assertion */}
        {hasCustomArea(item) && (
          <RowItem
            title={t('CUSTOM_AREA', {
              t: (item as CarpetItem).customArea!.toString(),
            })}
            value={` x${(item as CarpetItem).quantityCustomArea}`}
          />
        )}
      </BlockView>
    );
  };

  return (
    <BlockView style={{ marginTop: Spacing.SPACE_12 }}>
      <CText
        bold
        style={styles.txtHeaderTitle}>
        {title}
      </CText>
      <BlockView
        row
        jBetween
        style={styles.sofaItemStyle}>
        <BlockView>
          <CText style={styles.txtValueTitle}>{typeLabel}</CText>
        </BlockView>
        <BlockView>{data.map(renderServiceItem)}</BlockView>
      </BlockView>
    </BlockView>
  );
};

const BedDetails: React.FC<BedDetailsProps> = ({ data }) => {
  const { t } = useI18n();

  return (
    <BlockView style={{ marginTop: Spacing.SPACE_12 }}>
      <BlockView
        row
        jBetween
        style={styles.sofaItemStyle}>
        <CText
          bold
          style={styles.txtHeaderTitle}>
          {t('BED')}
        </CText>
        <BlockView>
          <ItemTypes types={data} />
        </BlockView>
      </BlockView>
    </BlockView>
  );
};

// Main component
export const DetailSofa: React.FC = () => {
  const { t } = useI18n();
  const { sofa, curtain, bed, carpet } = usePostTaskStore();

  const hasItems = <T,>(items: T[] | undefined): items is T[] => {
    return Boolean(items && items.length > 0);
  };

  return (
    <BlockView style={styles.container}>
      {hasItems(sofa) && (
        <ServiceDetailItem
          title={t('SOFA')}
          data={sofa}
          typeLabel={t('SOFA_TYPE_OF_SOFA')}
        />
      )}

      {hasItems(bed) && <BedDetails data={bed} />}

      {hasItems(curtain) && (
        <ServiceDetailItem
          title={t('CURTAIN')}
          data={curtain}
          typeLabel={t('CURTAIN_TYPE_TITLE')}
        />
      )}

      {hasItems(carpet) && (
        <ServiceDetailItem
          title={t('CARPET')}
          data={carpet}
          typeLabel={t('CARPET_TYPE_TITLE')}
        />
      )}
    </BlockView>
  );
};
