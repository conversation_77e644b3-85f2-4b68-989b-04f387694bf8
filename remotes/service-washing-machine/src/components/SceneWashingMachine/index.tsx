import React, { useRef } from 'react';
import {
  BlockView,
  ConditionView,
  CText,
  FastImage,
  ProcessButton,
  ScrollView,
  ServiceGuaranteed,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { CreateWashingMachineButton } from '../CreateWashingMachineButton';
import { ProcessModal } from '../ProcessModal';
import { WashingMachineItem } from '../WashingMachineItem';
import { styles } from './styles';

type SceneWashingMachineProps = {
  route: any;
  tasksWashingMachine: any[];
  onRemoveItem: (index: number) => void;
  onCreate: (task: any) => void;
};

export const SceneWashingMachine = ({
  route,
  tasksWashingMachine,
  onRemoveItem,
  onCreate,
}: SceneWashingMachineProps) => {
  const { t } = useI18n();
  const modalProcessRef = useRef();

  const openProcessModal = () => {
    // trackingServiceClick({
    //   screenName: TrackingScreenNames.DetailInformation,
    //   serviceName: SERVICES.WASHING_MACHINE,
    //   action: TRACKING_ACTION.ViewWorkingProcess,
    //   isTetBooking: service?.isTet,
    // });
    modalProcessRef.current?.open && modalProcessRef.current?.open();
  };

  const closeProcessModal = () => {
    modalProcessRef.current?.close && modalProcessRef.current?.close();
  };

  return (
    <>
      <ScrollView
        testID={`scrollTabStep2WM-${route.name}`}
        showsVerticalScrollIndicator={false}
      >
        <BlockView
          flex
          style={styles.container}
        >
          <BlockView
            flex
            style={styles.imageContainer}
          >
            <FastImage
              resizeMode="cover"
              source={{ uri: route.image }}
              style={styles.image}
            />
          </BlockView>
          <BlockView
            flex
            style={styles.contentContainer}
          >
            <CreateWashingMachineButton
              type={route}
              onCreate={onCreate}
            />
            <ConditionView
              condition={Boolean(tasksWashingMachine?.length)}
              viewTrue={
                <BlockView style={styles.tasksContainer}>
                  <CText
                    bold
                    style={styles.titleListTxt}
                  >
                    {t('LIST_WASHING_MACHINE')}
                  </CText>
                  <SizedBox height={Spacing.SPACE_12} />
                  {tasksWashingMachine?.map((item, index) => {
                    const isLastItem = index === tasksWashingMachine.length - 1;
                    const borderBottomWidth = isLastItem ? 0 : 1;
                    return (
                      <WashingMachineItem
                        index={index}
                        item={item}
                        key={index.toString()}
                        style={[styles.itemContainer, { borderBottomWidth }]}
                        onRemove={() => onRemoveItem?.(index)}
                      />
                    );
                  })}
                </BlockView>
              }
            />
            <SizedBox height={Spacing.SPACE_16} />
            <ProcessButton onPress={openProcessModal} />
            <ServiceGuaranteed />
          </BlockView>
        </BlockView>
      </ScrollView>
      <ProcessModal
        modalRef={modalProcessRef}
        data={route}
        onPressUnderstood={closeProcessModal}
      />
    </>
  );
};
