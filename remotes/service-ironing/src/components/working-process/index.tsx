import React from 'react';
import {
  <PERSON>View,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  getTextWithLocale,
  IObjectText,
  Markdown,
  ScrollView,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { styles } from './styles';

const RATIO_HEIGHT = 1.5;
type IDetailWorkingProcess = {
  name?: string;
  text?: IObjectText;
  workToDo?: IObjectText[];
};

export const WorkingProcess = ({
  detail,
}: {
  detail?: IDetailWorkingProcess[];
}) => {
  if (isEmpty(detail)) {
    return null;
  }

  return (
    <BlockView
      maxHeight={Math.round(DeviceHelper.WINDOW.HEIGHT / RATIO_HEIGHT)}>
      <ScrollView
        bounces={false}
        showsVerticalScrollIndicator={false}>
        <Markdown
          text={getTextWithLocale(detail?.[0]?.text)}
          textStyle={styles.txtMarkdown}
        />
        {detail?.[0]?.workToDo?.map((item) => (
          <BlockView key={getTextWithLocale(item)}>
            <Markdown
              text={getTextWithLocale(item)}
              textStyle={styles.txtMarkdown}
            />
          </BlockView>
        ))}
      </ScrollView>
    </BlockView>
  );
};
