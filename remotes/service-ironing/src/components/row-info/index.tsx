import React, { ReactNode } from 'react';
import { TextStyle } from 'react-native';
import {
  BlockView,
  BlockViewProps,
  ColorsV2,
  CommonTextProps,
  ConditionView,
  CText,
  Maybe,
  Spacing,
} from '@btaskee/design-system';

type InfoProps = {
  testID?: string;
  value?: Maybe<string | ReactNode>;
  quantity?: Maybe<number>;
  styleInfo?: TextStyle;
  lineHeight?: number;
} & CommonTextProps;

type RowInfoProps<T> = {
  testID?: string;
  label?: string;
  value?: InfoProps['value'];
  lineHeight?: InfoProps['lineHeight'];
  quantity?: number;
  items?: T[];
  flexLeft?: number;
  flexRight?: number;
  itemAsValue?: (item?: T) => string;
  itemAsQuantity?: (item?: T) => Maybe<number>;
  styleInfo?: TextStyle;
  labelProps?: CommonTextProps;
  infoProps?: CommonTextProps;
} & BlockViewProps;

const LINE_HEIGHT_DEFAULT = 30;

const Info = ({
  testID,
  value,
  quantity,
  lineHeight = LINE_HEIGHT_DEFAULT,
  styleInfo,
  ...props
}: InfoProps) => {
  const quantityTxt = ` x${quantity}`;
  return (
    <CText
      testID={testID}
      flex
      right
      lineHeight={lineHeight}
      style={styleInfo}
      {...props}>
      {typeof value === 'string' ? value?.trim() : value}
      <ConditionView
        condition={Boolean(quantity)}
        viewTrue={
          <CText
            bold
            color={ColorsV2.orange500}>
            {quantityTxt}
          </CText>
        }
      />
    </CText>
  );
};

export const RowInfo: <T>(props: RowInfoProps<T>) => ReactNode = ({
  testID,
  label,
  value,
  quantity,
  items,
  flexLeft = 0.38,
  flexRight = 0.6,
  lineHeight = LINE_HEIGHT_DEFAULT,
  itemAsValue,
  itemAsQuantity,
  styleInfo = {},
  labelProps,
  infoProps,
  ...props
}) => {
  return (
    <BlockView
      row
      justify="space-between"
      padding={{ vertical: Spacing.SPACE_04 }}
      {...props}>
      <ConditionView
        condition={Boolean(label)}
        viewTrue={
          <CText
            lineHeight={lineHeight}
            color={ColorsV2.neutral400}
            flex={flexLeft}
            {...labelProps}>
            {label}
          </CText>
        }
      />
      <BlockView flex={flexRight}>
        <ConditionView
          condition={Boolean(value) && !items?.length}
          viewTrue={
            <Info
              testID={testID}
              value={value}
              quantity={quantity}
              styleInfo={styleInfo}
              lineHeight={lineHeight}
              {...infoProps}
            />
          }
          viewFalse={
            <BlockView>
              {items?.map?.((item, index) => {
                return (
                  <Info
                    key={index.toString()}
                    value={itemAsValue?.(item)}
                    quantity={itemAsQuantity?.(item)}
                    lineHeight={lineHeight}
                    {...infoProps}
                  />
                );
              })}
            </BlockView>
          }
        />
      </BlockView>
    </BlockView>
  );
};
