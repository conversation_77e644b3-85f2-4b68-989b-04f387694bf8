import React, { useMemo } from 'react';
import {
  BlockView,
  Card,
  CText,
  getTextWithLocale,
  IObjectText,
  TouchableOpacity,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { styles } from './styles';

type IParamTrackChangeDuration = { oldDuration?: number; newDuration?: number };

export type IOptionIron = {
  name?: string;
  title?: IObjectText;
  description?: IObjectText;
  duration?: number;
};

export type DurationProps = {
  duration?: number;
  listDuration?: IOptionIron[];
  onChange: (duration: number) => void;
  trackingChangeDuration?: ({
    oldDuration,
    newDuration,
  }: IParamTrackChangeDuration) => void;
};

export const DurationIroning = ({
  listDuration,
  duration,
  onChange,
  trackingChangeDuration,
}: DurationProps) => {
  const handleClick = (newDuration: number = 0) => {
    trackingChangeDuration &&
      trackingChangeDuration({ oldDuration: duration, newDuration });
    onChange && onChange(newDuration);
  };

  const checkActive = useMemo(
    () => (newDuration?: number) => {
      return Boolean(duration && duration === newDuration);
    },
    [duration],
  );

  if (isEmpty(listDuration)) return <BlockView />;

  return (
    <BlockView>
      {listDuration?.map((item, index) => {
        const isActive = checkActive(item?.duration);
        const onPress = () => handleClick(item?.duration);

        return (
          <TouchableOpacity
            key={index}
            onPress={onPress}
            testID={`chooseDuration-${item.duration}`}
            style={styles.wrapStyle}>
            <Card
              row
              center
              style={[styles.wrapButton, isActive ? styles.borderActive : {}]}>
              <BlockView style={styles.leftContent}>
                <CText
                  bold
                  style={[
                    styles.txtDuration,
                    isActive ? styles.textActive : {},
                  ]}>
                  {getTextWithLocale(item?.title)}
                </CText>
                <CText style={styles.txtArea}>
                  {getTextWithLocale(item?.description)}
                </CText>
              </BlockView>
              <BlockView />
            </Card>
          </TouchableOpacity>
        );
      })}
    </BlockView>
  );
};
