import { StyleSheet } from 'react-native';
import { ColorsV2, FontSizes, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  borderActive: {
    borderColor: ColorsV2.orange500,
  },
  textActive: {
    color: ColorsV2.orange500,
  },
  txtArea: {
    color: ColorsV2.neutral500,
  },
  txtDuration: {
    marginBottom: Spacing.SPACE_08,
    fontSize: FontSizes.SIZE_14,
  },
  wrapButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.SPACE_16,
  },
  leftContent: {
    flex: 1,
  },
  wrapStyle: {
    marginBottom: Spacing.SPACE_16,
  },
});
