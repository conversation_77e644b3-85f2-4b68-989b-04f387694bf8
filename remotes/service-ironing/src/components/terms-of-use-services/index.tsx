/**
 * Component that displays terms of service information for Thailand users.
 * Shows different content based on the selected payment method.
 */
import React, { useMemo } from 'react';
import {
  BlockView,
  ISO_CODE,
  PAYMENT_METHOD,
  useAppStore,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@stores';

import { useI18n } from '@hooks';

import { SeeMore } from '../see-more';

/**
 * Displays payment terms and conditions for Thailand users
 * Content varies based on the selected payment method
 */
const TermsOfUseServices: React.FC = () => {
  const { t } = useI18n();
  const { isoCode } = useAppStore();
  const { paymentMethod } = usePostTaskStore();

  // Generate content based on payment method
  const content = useMemo(() => {
    const paymentValue = paymentMethod?.value;

    if (
      paymentValue === PAYMENT_METHOD.shopeePay ||
      paymentValue === PAYMENT_METHOD.trueMoney
    ) {
      return (
        t('TERM_OF_PAYMENT_METHOD_E_WALL.TERM_1') +
        '\n' +
        t('TERM_OF_PAYMENT_METHOD_E_WALL.TERM_2') +
        '\n' +
        t('TERM_OF_PAYMENT_METHOD_E_WALL.TERM_3') +
        '\n' +
        t('TERM_OF_PAYMENT_METHOD_E_WALL.TERM_4')
      );
    }

    if (paymentValue === PAYMENT_METHOD.card) {
      return (
        t('TERM_OF_PAYMENT_METHOD_CARD.TERM_1') +
        '\n' +
        t('TERM_OF_PAYMENT_METHOD_CARD.TERM_2') +
        '\n   ' +
        t('TERM_OF_PAYMENT_METHOD_CARD.TERM_2_1') +
        '\n   ' +
        t('TERM_OF_PAYMENT_METHOD_CARD.TERM_2_2') +
        '\n   ' +
        t('TERM_OF_PAYMENT_METHOD_CARD.TERM_2_3') +
        '\n' +
        t('TERM_OF_PAYMENT_METHOD_CARD.TERM_3')
      );
    }

    return (
      t('TERMS_OF_USED_SERVICES_1_TH') +
      '\n' +
      t('TERMS_OF_USED_SERVICES_2_TH') +
      '\n' +
      t('TERMS_OF_USED_SERVICES_3_TH') +
      '\n   ' +
      t('TERMS_OF_USED_SERVICES_3_1_TH') +
      '\n   ' +
      t('TERMS_OF_USED_SERVICES_3_2_TH') +
      '\n' +
      t('TERMS_OF_USED_SERVICES_4_TH') +
      '\n' +
      t('TERMS_OF_USED_SERVICES_5_TH') +
      '\n'
    );
  }, [paymentMethod, t]);

  // Only show for Thailand users
  if (isoCode !== ISO_CODE.TH) {
    return null;
  }

  return (
    <BlockView
      testID="terms-of-use-services-container"
      padding={{ top: 20 }}
    >
      <SeeMore
        title={t('TERMS_OF_USED_SERVICES_TH')}
        content={content}
      />
    </BlockView>
  );
};

export default TermsOfUseServices;
