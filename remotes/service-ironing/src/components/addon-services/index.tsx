import React, { useCallback, useMemo } from 'react';
import {
  BlockView,
  Card,
  ColorsV2,
  CText,
  <PERSON>ceHelper,
  FontSizes,
  getCurrency,
  IconImage,
  Requirement,
  REQUIREMENTS_TYPE,
  showPriceAndCurrency,
  Spacing,
  TouchableOpacity,
  useAppStore,
} from '@btaskee/design-system';
import { get, isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

/**
 * Props for the AddOnServices component
 */
interface AddOnServicesProps {
  /** Selected requirements */
  data: Requirement[];
  /** Callback when a requirement is selected/deselected */
  onChange: (requirement: Requirement) => void;
  /** Whether premium service is selected */
  isPremium?: boolean;
}

/**
 * Component for displaying and selecting add-on services
 */
export const AddOnServices: React.FC<AddOnServicesProps> = ({
  data,
  onChange,
  isPremium,
}) => {
  const { t } = useI18n();
  const { locale } = useAppStore();
  const currency = getCurrency();
  const { forceTasker, service } = usePostTaskStore();

  /**
   * Handles click on a requirement item
   */
  const handleClick = useCallback(
    (requirement: Requirement) => () => {
      if (!requirement) return;
      onChange(requirement);
    },
    [onChange],
  );

  /**
   * Checks if a requirement is active (selected)
   */
  const checkActive = useMemo(
    () =>
      (requirement: Requirement): boolean => {
        if (!requirement || !data) return false;
        const isExist = data.find(
          (e: Requirement) => e && requirement?.type === e.type,
        );
        return Boolean(isExist);
      },
    [data],
  );

  /**
   * Get requirements from service or default to empty array
   */
  const requirements: Requirement[] = get(service, 'requirements', []) || [];

  /**
   * Renders the service items based on requirements and conditions
   */
  const shouldRenderService = useMemo(() => {
    if (!requirements || requirements.length === 0) return null;

    let requirePrimary = requirements;

    // If premium service is selected or force tasker doesn't have cleaning kit,
    // only show first two requirements
    if (isPremium || (!isEmpty(forceTasker) && !forceTasker?.hasCleaningKit)) {
      requirePrimary = requirements.filter(
        (requirement: Requirement) =>
          requirement?.type !== REQUIREMENTS_TYPE.CLEANING_TOOLS,
      );
    }

    return requirePrimary.map((requirement: Requirement, index: number) => {
      if (!requirement) return null;

      const active = checkActive(requirement);
      const costText = requirement?.duration
        ? `+${requirement?.duration}h`
        : `+${showPriceAndCurrency(requirement?.cost || 0)}`;

      const iconSource = { uri: requirement?.icon || '' };
      const textValue =
        requirement?.text && requirement?.text?.[locale]
          ? requirement?.text?.[locale]
          : '';

      return (
        <BlockView
          style={styles.serviceItemContainer}
          key={index}>
          <TouchableOpacity
            testID={`addon-service-${index}`}
            onPress={handleClick(requirement)}
            style={styles.buttonService}>
            <Card
              center
              style={active ? styles.borderActive : {}}>
              <IconImage
                source={iconSource}
                style={styles.image}
                color={active ? ColorsV2.orange500 : ColorsV2.neutral400}
              />
            </Card>
          </TouchableOpacity>
          <CText
            numberOfLines={2}
            bold
            style={[styles.txtName, active ? styles.textActive : {}]}>
            {textValue}
          </CText>
          <CText style={[styles.txtPrice, active ? styles.textActive : {}]}>
            {costText}
          </CText>
        </BlockView>
      );
    });
  }, [requirements, isPremium, locale, forceTasker, checkActive, handleClick]);

  // Early return if no requirements
  if (!requirements || isEmpty(requirements)) return null;

  return (
    <BlockView style={styles.containerAddOnService}>
      <CText
        bold
        size={FontSizes.SIZE_20}
        style={styles.txtPanel}>
        {t('SV_HC_SCR2_DETAIL_EXTRA_SV_TITLE')}
      </CText>
      <CText margin={{ bottom: Spacing.SPACE_24 }}>
        {t('SV_HC_SCR2_DETAIL_EXTRA_SV_NOTE')}
      </CText>
      <BlockView
        row
        style={styles.content}>
        {shouldRenderService}
      </BlockView>
    </BlockView>
  );
};
