import { Dimensions, StyleSheet } from 'react-native';
import {
  ColorsV2,
  <PERSON><PERSON><PERSON><PERSON>per,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

const { width } = Dimensions.get('window');
const SIZE_IMAGE = Math.round(width / 7);

export const styles = StyleSheet.create({
  borderActive: {
    borderColor: ColorsV2.orange500,
  },
  textActive: {
    color: ColorsV2.orange500,
    fontSize: FontSizes.SIZE_12,
    textAlign: 'center',
  },
  image: {
    height: SIZE_IMAGE,
    width: SIZE_IMAGE,
  },
  txtName: {
    marginTop: Spacing.SPACE_16,
    fontSize: FontSizes.SIZE_12,
    textAlign: 'center',
  },
  containerAddOnService: {
    marginTop: Spacing.SPACE_08,
  },
  txtPanel: {
    marginVertical: Spacing.SPACE_16,
  },
  content: {
    justifyContent: 'space-around',
  },
  txtPrice: {
    textAlign: 'center',
    color: ColorsV2.neutral400,
    fontSize: FontSizes.SIZE_12,
    marginTop: 5,
  },
  buttonService: {
    // Empty style object kept for consistency with existing code
  },
  serviceItemContainer: {
    width: Math.round(DeviceHelper.WINDOW.WIDTH / 4),
  },
});
