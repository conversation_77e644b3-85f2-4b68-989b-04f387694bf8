import React from 'react';
import { StyleSheet, ViewStyle } from 'react-native';
import {
  AddonsName,
  BlockView,
  ConditionView,
  CText,
  FontSizes,
  GENDER,
  IObjectText,
  OptionalAutoChooseTasker,
  OptionalChooseFavouriteTasker,
  OptionalChooseGender,
  OptionalChoosePet,
  SERVICES,
  Spacing,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@stores';
import { isEmpty } from 'lodash-es';

import { useI18n, usePostTask } from '@hooks';

export type IAddons = {
  name?: string;
  cost?: number;
  text?: IObjectText;
  // status?: StatusOption;
};

interface OptionalProps {
  optional?: any;
  blacklist?: any;
  pet?: any;
  gender?: GENDER | null;
  style?: ViewStyle;
  onChangePet?: (value: any) => void;
  addons?: IAddons[];
  setAddons?: (addons: IAddons[]) => void;
  addonsSelected?: IAddons[];
}

const Optional = ({
  style,
  optional,
  blacklist,
  pet,
  gender,
  addons,
  onChangePet,
  setAddons,
  addonsSelected,
}: OptionalProps) => {
  const { t } = useI18n();
  const forceTasker = {};
  const {
    isAutoChooseTasker,
    isFavouriteTasker,
    setIsAutoChooseTasker,
    setIsFavouriteTasker,
    setGender,
  } = usePostTaskStore();
  const { getPrice } = usePostTask();

  const shouldRenderAutoChooseTasker = React.useMemo(() => {
    // ON/OFF from service
    if (!optional?.isAutoChooseTaskerEnabled) {
      return null;
    }
    return (
      <OptionalAutoChooseTasker
        serviceName={SERVICES.CLEANING}
        isAutoChooseTasker={isAutoChooseTasker}
        setAutoChooseTasker={async (value) => {
          setIsAutoChooseTasker(value);
          await getPrice();
        }}
      />
    );
  }, [isAutoChooseTasker]);

  const shouldRenderPet = React.useMemo(() => {
    if (!onChangePet) {
      return null;
    }
    const petOption = addons?.find((item) => item?.name === AddonsName.Pet);
    return (
      <OptionalChoosePet
        onChangePet={onChangePet}
        pet={pet}
        petOption={petOption}
        setAddons={setAddons}
        addons={addonsSelected}
      />
    );
  }, [pet, addons, addonsSelected]);

  const shouldRenderFavoriteTasker = React.useMemo(() => {
    if (blacklist && blacklist.indexOf('FAV_TASKER') !== -1) {
      return null;
    }
    return (
      <OptionalChooseFavouriteTasker
        blacklist={blacklist}
        isFavouriteTasker={isFavouriteTasker}
        setIsFavouriteTasker={setIsFavouriteTasker}
      />
    );
  }, [blacklist, isFavouriteTasker]);

  const shouldRenderChooseGender = React.useMemo(() => {
    if (!optional?.isGenderEnabled) {
      return null;
    }
    return (
      <OptionalChooseGender
        gender={gender}
        setGender={setGender}
      />
    );
  }, [gender]);

  return (
    <BlockView style={[styles.container, style]}>
      <CText
        bold
        size={FontSizes.SIZE_20}
        style={styles.txtPanel}
      >
        {t('PT1_DETAIL_OPTION_TITLE')}
      </CText>
      <BlockView style={styles.content}>
        {shouldRenderPet}
        <ConditionView
          condition={isEmpty(forceTasker)}
          viewTrue={
            <>
              {shouldRenderAutoChooseTasker}
              {shouldRenderFavoriteTasker}
              {shouldRenderChooseGender}
            </>
          }
        />
      </BlockView>
    </BlockView>
  );
};

const styles = StyleSheet.create({
  content: {},

  container: {
    marginTop: '6%',
  },
  txtPanel: {
    marginTop: Spacing.SPACE_16,
    marginBottom: Spacing.SPACE_20,
  },
});

export default Optional;
