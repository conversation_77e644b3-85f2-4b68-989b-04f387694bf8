import React from 'react';
// import { setForceTasker } from '@action/post-task';
// import CheckBoxComponent from '@component/checkbox';
// import { useAppDispatch, useAppSelector } from '@hooks/app-redux';
// import { PostTaskSelector } from '@reducer/post-task/post-task.selector';
import { BlockView, CheckBox, CText } from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

const OptionBookingWithFavTasker = () => {
  const { t } = useI18n();
  // const dispatch = useAppDispatch();
  const [checked, setChecked] = React.useState(false);
  // const forceTasker = useAppSelector(PostTaskSelector.forceTasker);

  const handleChange = (value: boolean) => {
    setChecked(value);
    // dispatch(setForceTasker({ ...forceTasker, isResent: value }));
  };

  return (
    <BlockView style={styles.container}>
      <BlockView style={styles.boxText}>
        <CText style={styles.title}>{t('FAV_TASKER.ADD_OPTION_NOTE')}</CText>
      </BlockView>
      <CheckBox
        containerStyle={styles.containerStyle}
        title={t('FAV_TASKER.ADD_OPTION_CHECK_BOOK')}
        onChecked={handleChange}
        checked={checked}
      />
      <BlockView style={styles.boxText}>
        <CText style={styles.title2}>{t('FAV_TASKER.ADD_OPTION_NOTE_2')}</CText>
      </BlockView>
    </BlockView>
  );
};
export default OptionBookingWithFavTasker;
