import React from 'react';
import {
  BlockView,
  BorderRadius,
  ColorsV2,
  CText,
  IconAssets,
  IconImage,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

export const PrepareTools = () => {
  const { t } = useI18n();
  return (
    <BlockView
      row
      margin={{ top: Spacing.SPACE_32 }}
      radius={BorderRadius.RADIUS_08}
      padding={Spacing.SPACE_16}
      border={{ width: 1, color: ColorsV2.green500 }}>
      <IconImage
        source={IconAssets.icInfoFill}
        color={ColorsV2.green500}
      />
      <CText
        flex
        margin={{ left: Spacing.SPACE_12 }}>
        {t('IRONING_SERVICE.PREPARE_IRONING_TOOL')}
      </CText>
    </BlockView>
  );
};
