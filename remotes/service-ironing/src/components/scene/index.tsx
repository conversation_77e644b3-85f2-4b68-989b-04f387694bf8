/**
 * Scene Component - Displays a list of work items to be done
 * Renders a scrollable card with work items based on route data
 */
import React, { memo } from 'react';
import {
  BlockView,
  Card,
  ColorsV2,
  ConditionView,
  CText,
  getTextWithLocale,
  IconAssets,
  IconImage,
  ScrollView,
  SizedBox,
  Spacing,
  useAppStore,
} from '@btaskee/design-system';

import { styles } from './styles';

interface WorkToDo {
  [key: string]: string;
}

interface RouteParams {
  workToDo?: WorkToDo[];
}

interface SceneProps {
  route?: RouteParams;
}

const WorkingItem = ({
  title,
  isShowDivider,
}: {
  title?: string;
  isShowDivider?: boolean;
}) => {
  return (
    <BlockView>
      <BlockView row>
        <IconImage
          source={IconAssets.icStar}
          size={20}
        />
        <CText margin={{ left: Spacing.SPACE_12 }}>{title}</CText>
      </BlockView>
      <ConditionView
        condition={Boolean(isShowDivider)}
        viewTrue={
          <SizedBox
            height={1}
            color={ColorsV2.neutral100}
            margin={{ vertical: Spacing.SPACE_12 }}
          />
        }
      />
    </BlockView>
  );
};
/**
 * Renders a list of work items from the route parameters
 */
export const Scene: React.FC<SceneProps> = memo(({ route }: SceneProps) => {
  const { locale } = useAppStore();
  const workItems = route?.workToDo || [];

  return (
    <ScrollView
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}>
      <Card>
        {workItems.map((item, index) => {
          const isShowDivider = index !== workItems.length - 1;
          const title = getTextWithLocale(item, locale) || '';

          return (
            <WorkingItem
              key={`workToDo-${index}`}
              title={title}
              isShowDivider={isShowDivider}
            />
          );
        })}
      </Card>
    </ScrollView>
  );
});

Scene.displayName = 'Scene';
