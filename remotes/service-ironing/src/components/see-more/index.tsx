import React, { useState } from 'react';
import Icon from 'react-native-vector-icons/FontAwesome5';
import {
  AnimationHelpers,
  BlockView,
  ColorsV2,
  CText,
  HitSlop,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

type SeeMoreProps = {
  title: string;
  content: string;
  numberOffline?: number;
  navigate?: () => void;
};
export const SeeMore = ({
  content,
  title,
  numberOffline = 3,
  navigate,
}: SeeMoreProps) => {
  const { t } = useI18n();
  const [isSeeMore, setIsSeeMore] = useState(false);
  const handleSeeMore = () => {
    if (navigate) {
      navigate();
      return;
    }
    AnimationHelpers.runLayoutAnimation();
    setIsSeeMore((preSeeMore) => !preSeeMore);
  };
  return (
    <BlockView
      align="flex-start"
      margin={{ bottom: 10 }}>
      <BlockView
        height={isSeeMore ? 'auto' : 120}
        overflow="hidden">
        <CText margin={{ bottom: Spacing.SPACE_16 }}>{title}</CText>
        <CText
          color={ColorsV2.neutral400}
          padding={{ left: Spacing.SPACE_16 }}
          size={13}
          margin={{ bottom: Spacing.SPACE_12 }}
          lineHeight={21}
          numberOfLines={isSeeMore ? 0 : numberOffline}>
          {content}
        </CText>
      </BlockView>
      <BlockView alignSelf="flex-end">
        <TouchableOpacity
          testID="see-more-toggle-button"
          hitSlop={HitSlop.MEDIUM}
          onPress={handleSeeMore}>
          <BlockView
            row
            center>
            <CText
              color={ColorsV2.orange500}
              margin={{ right: 5 }}>
              {t(isSeeMore ? 'HIDE_PREMIUM_TOOLS_DETAIL' : 'SEE_MORE')}
            </CText>
            <Icon
              name={isSeeMore ? 'angle-double-up' : 'angle-double-down'}
              size={10}
              color={ColorsV2.orange500}
            />
          </BlockView>
        </TouchableOpacity>
      </BlockView>
    </BlockView>
  );
};
