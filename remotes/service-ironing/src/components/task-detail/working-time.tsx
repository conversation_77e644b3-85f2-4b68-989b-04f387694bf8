import React from 'react';
import {
  BlockView,
  CText,
  DateTimeHelpers,
  DateWithGMT,
  DurationWithGMT,
  IDate,
  TypeFormatDate,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';

import { styles } from './styles';

const WorkingTime = ({
  date,
  duration,
  schedule,
  isEnableSchedule,
  timezone,
  dateOptions,
}: {
  date: IDate;
  duration: number;
  schedule?: number[];
  isEnableSchedule: boolean;
  timezone: string;
  dateOptions?: IDate[];
}) => {
  const isDisabledWorkingDay = !isEmpty(dateOptions);

  const { t } = useI18n();

  const shouldRenderSchedule = React.useMemo(() => {
    if (isEnableSchedule && !isEmpty(schedule)) {
      return (
        <BlockView style={styles.group}>
          <CText style={styles.txtVLabel}>
            {t('POST_TASK_CHECKBOX_REPEAT')}
          </CText>
          <CText style={styles.txtValue}>
            {schedule
              .map((day) => {
                const text = DateTimeHelpers.formatToString({
                  timezone,
                  date: DateTimeHelpers.toDayTz({ timezone }).day(Number(day)),
                  typeFormat: TypeFormatDate.DayAbbreviated,
                });
                return text;
              })
              .join(', ')}
          </CText>
        </BlockView>
      );
    }
    return null;
  }, [isEnableSchedule, schedule, timezone]);

  const shouldRenderDuration = React.useMemo(() => {
    return (
      <BlockView style={styles.group}>
        <CText style={styles.txtVLabel}>{t('WORK_IN')}</CText>
        <DurationWithGMT
          testID={'duration'}
          style={styles.txtValue}
          date={date}
          timezone={timezone}
          duration={duration}
          isShowOnlyDuration={isDisabledWorkingDay}
        />
      </BlockView>
    );
  }, [date, timezone, duration, isDisabledWorkingDay]);

  const shouldRenderWorkingDay = React.useMemo(() => {
    if (isDisabledWorkingDay) return null;
    return (
      <BlockView style={styles.group}>
        <CText style={styles.txtVLabel}>{t('WORKING_DAY')}</CText>
        <DateWithGMT
          testID={'workingDay'}
          timezone={timezone}
          date={date}
          typeFormat={TypeFormatDate.DateTimeFullWithDay}
          style={styles.txtValue}
        />
      </BlockView>
    );
  }, [date, timezone, isDisabledWorkingDay]);

  return (
    <BlockView>
      <CText
        bold
        style={styles.subPanel}>
        {t('TIME_TO_WORK')}
      </CText>

      {shouldRenderWorkingDay}

      {shouldRenderDuration}

      {shouldRenderSchedule}
    </BlockView>
  );
};
export default WorkingTime;
