import React from 'react';
import { BlockView, CText, FontSizes } from '@btaskee/design-system';

import { useI18n } from '@hooks';

import Detail from './detail';
import { styles } from './styles';

const TaskDetail = () => {
  const { t } = useI18n();

  return (
    <BlockView>
      <BlockView style={styles.panel}>
        <CText
          size={FontSizes.SIZE_16}
          testID="infoTask"
          bold
          style={styles.txtPanel}>
          {t('TASK_INFO')}
        </CText>
      </BlockView>
      <Detail />
    </BlockView>
  );
};

export default TaskDetail;
