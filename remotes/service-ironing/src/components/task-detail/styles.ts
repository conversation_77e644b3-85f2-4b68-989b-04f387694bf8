/**
 * Styles for the Confirm and Payment screen
 *
 * This file contains all the styling for the confirmation and payment screen
 * including layout, spacing, colors, and typography.
 */
import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

/**
 * StyleSheet for the Confirm and Payment screen
 */
export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorsV2.neutral50,
  },
  content: {
    paddingHorizontal: Spacing.SPACE_16,
    paddingBottom: 150,
    backgroundColor: ColorsV2.neutralWhite,
  },
  lineWithBorder: {
    borderBottomWidth: 1,
    borderBottomColor: ColorsV2.neutral100,
    marginBottom: Spacing.SPACE_12,
    paddingBottom: Spacing.SPACE_12,
  },
  txtAC: {
    marginBottom: Spacing.SPACE_04,
  },
  panel: {
    marginTop: Spacing.SPACE_32,
    marginBottom: Spacing.SPACE_16,
    justifyContent: 'space-between',
  },
  txtPanel: {},
  subPanel: {
    marginBottom: Spacing.SPACE_12,
    color: ColorsV2.neutral900,
  },
  label: {
    color: ColorsV2.neutral400,
  },
  txtVLabel: {
    width: '35%',
    paddingRight: Spacing.SPACE_12,
    color: ColorsV2.neutral400,
  },
  txtValue: {
    width: '65%',
    textAlign: 'right',
  },
  txtValue2: {
    textAlign: 'right',
  },
  group: {
    flexDirection: 'row',
    paddingVertical: Spacing.SPACE_04,
    // alignItems: 'center',
  },
  txtAction: {
    color: ColorsV2.orange500,
  },
  txtAmountDryClean: {
    color: ColorsV2.orange500,
  },
  txtDryClean: {
    textAlign: 'right',
    color: ColorsV2.neutral500,
  },
  txtTermLaundry: {
    color: ColorsV2.neutral400,
    padding: Spacing.SPACE_16,
    fontSize: FontSizes.SIZE_12,
  },
  btnChange: {
    backgroundColor: ColorsV2.neutral200,
    justifyContent: 'center',
    alignSelf: 'center',
    paddingVertical: Spacing.SPACE_04,
    paddingHorizontal: Spacing.SPACE_12,
    borderRadius: BorderRadius.RADIUS_16,
  },
  wrapLocation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.SPACE_08,
  },
  wrapContact: {
    flex: 1,
    marginTop: Spacing.SPACE_04,
  },
  wrapNumberPhone: {
    flex: 1,
    flexDirection: 'row',
    marginTop: Spacing.SPACE_04,
  },
  containerLocation: {
    flex: 1,
    paddingBottom: Spacing.SPACE_08,
    marginBottom: Spacing.SPACE_08,
  },
  wrapDetail: {
    marginTop: Spacing.SPACE_16,
  },
  pricePanel: {
    marginTop: Spacing.SPACE_12,
    paddingHorizontal: Spacing.SPACE_16,
    justifyContent: 'space-between',
  },
  txtPrice: {
    textAlign: 'right',
    color: ColorsV2.neutral900,
  },
  txtPromotion: {
    textDecorationColor: ColorsV2.orange500,
    textDecorationLine: 'line-through',
    color: ColorsV2.orange500,
    textAlign: 'right',
  },
  txtTotal: {
    color: ColorsV2.neutral900,
  },
  wrapTaskerFavorite: {
    marginBottom: Spacing.SPACE_16,
    borderBottomWidth: 1,
    borderBottomColor: ColorsV2.neutral100,
    paddingBottom: Spacing.SPACE_04,
  },
  txtChangeToRegularBooking: {
    color: ColorsV2.orange500,
    fontSize: FontSizes.SIZE_16,
  },
  wrapChangeToRegularBooking: {
    marginTop: Spacing.SPACE_20,
  },
  txtChangeToRegularBookingDescription: {
    marginTop: 5,
    fontSize: FontSizes.SIZE_12,
  },
});
