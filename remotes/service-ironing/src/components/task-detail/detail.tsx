/**
 * Component that displays detailed information about the cleaning task
 * including duration, workload, and other service-specific details.
 */
import React from 'react';
import {
  BlockView,
  Card,
  ConditionView,
  DateTimeHelpers,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

import TaskerFavoriteForRebook from '../tasker-favorite-for-rebook';
import Note from './note';
import { styles } from './styles';
import WorkingTime from './working-time';

/**
 * Displays detailed information about the cleaning task
 */
const Detail: React.FC = () => {
  const {
    forceTasker,
    date,
    duration,
    note,
    schedule,
    isEnabledSchedule,
    address,
  } = usePostTaskStore();
  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

  return (
    <Card>
      <ConditionView
        condition={!!forceTasker && !isEmpty(forceTasker)}
        viewTrue={
          <BlockView style={styles.wrapTaskerFavorite}>
            <TaskerFavoriteForRebook forceTasker={forceTasker} />
          </BlockView>
        }
      />
      <WorkingTime
        date={date ?? ''}
        schedule={schedule ?? []}
        duration={duration ?? 0}
        isEnableSchedule={!!isEnabledSchedule}
        timezone={timezone ?? ''}
      />
      <Note note={note ?? ''} />
    </Card>
  );
};

export default Detail;
