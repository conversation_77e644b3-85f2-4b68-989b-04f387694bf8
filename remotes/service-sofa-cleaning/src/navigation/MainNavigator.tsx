import '../i18n';

import React, { useEffect } from 'react';
import {
  Colors,
  FontFamily,
  IconAssets,
  IconImage,
  SERVICES,
  TouchableOpacity,
  useSettingsStore,
} from '@btaskee/design-system';
import {
  createNativeStackNavigator,
  type NativeStackNavigationOptions,
} from '@react-navigation/native-stack';

import { useI18n } from '@hooks';
import { MainStackParamList } from '@navigation/type';
import {
  ChooseAddress,
  ChooseDateTime,
  ChooseInfoSofa,
  ChooseService,
  ConfirmAndPayment,
  IntroService,
  PostTaskSuccess,
} from '@screens';
import { usePostTaskStore } from '@stores';

import { RouteName } from './RouteName';

const Stack = createNativeStackNavigator<MainStackParamList>();

const MainNavigator = () => {
  const { t } = useI18n();

  const { setService } = usePostTaskStore();
  const settings = useSettingsStore().settings;

  useEffect(() => {
    initData();
  }, []);

  // TODO: init data for cleaning service
  const initData = async () => {
    const sofaService = settings?.services?.find(
      (service) => service?.name === SERVICES.SOFA,
    );

    setService(sofaService);
  };

  const renderHeaderLeft = ({
    navigation,
    colorIcon,
  }: {
    navigation: any;
    colorIcon?: Colors;
  }) => {
    return (
      <TouchableOpacity
        onPress={() => navigation?.goBack()}
        activeOpacity={0.7}>
        <IconImage
          source={IconAssets.icBack}
          size={24}
          color={colorIcon || Colors.BLACK}
        />
      </TouchableOpacity>
    );
  };
  return (
    <Stack.Navigator
      screenOptions={({ navigation }): NativeStackNavigationOptions => ({
        headerShown: true,
        headerLeft: () => renderHeaderLeft({ navigation }),
        animation: 'slide_from_right',
        animationDuration: 200,
        contentStyle: { backgroundColor: Colors.WHITE },
        headerStyle: {
          backgroundColor: Colors.WHITE,
        },
        headerTitleStyle: {
          color: Colors.BLACK,
          fontSize: 18,
          fontFamily: FontFamily.bold,
        },
      })}
      initialRouteName={RouteName.ChooseAddress}>
      <Stack.Screen
        name={RouteName.IntroService}
        component={IntroService}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={RouteName.ChooseAddress}
        component={ChooseAddress}
        options={{ title: t('LIST_OF_LOCATIONS') }}
      />
      <Stack.Screen
        name={RouteName.ChooseService}
        component={ChooseService}
      />
      <Stack.Screen
        name={RouteName.ChooseDateTime}
        component={ChooseDateTime}
        options={{ title: t('WORK_TIME_TITLE') }}
      />
      <Stack.Screen
        name={RouteName.ConfirmAndPayment}
        component={ConfirmAndPayment}
        options={{ title: t('PT2_CONFIRM_HEADER_TITLE') }}
      />
      <Stack.Screen
        name={RouteName.PostTaskSuccess}
        component={PostTaskSuccess}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={RouteName.ChooseInfoSofa}
        component={ChooseInfoSofa}
        options={{ title: t('SV_SOFA_SCR2_TAB_SOFA_TITLE') }}
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
