/**
 * Enhanced Test Utilities for Housekeeping E2E Tests
 * 
 * Provides high-level test utilities that combine scroll management,
 * performance monitoring, and sequential flow validation.
 */

const { element, by, waitFor } = require('detox');
const {
  TIMEOUTS,
  SCROLL_CONFIG,
  TEST_IDS,
  PERFORMANCE_BENCHMARKS,
  ERROR_CONFIG,
} = require('./test-config');

const {
  scrollToRevealElement,
  bookingFlowScroll,
  confirmationScreenScroll,
} = require('./scroll-helpers');

const {
  initData,
  tapId,
  waitForElement,
  expectElementVisible,
  typeToTextField,
  removeIntroHouseKeeping,
  tapIdService,
} = require('./step-definition');

/**
 * Performance monitoring wrapper
 * Tracks execution time and validates against benchmarks
 */
class PerformanceMonitor {
  constructor(testName) {
    this.testName = testName;
    this.startTime = Date.now();
    this.stepTimes = {};
    this.currentStep = null;
  }

  startStep(stepName) {
    if (this.currentStep) {
      this.endStep();
    }
    this.currentStep = stepName;
    this.stepTimes[stepName] = { start: Date.now() };
  }

  endStep() {
    if (this.currentStep && this.stepTimes[this.currentStep]) {
      this.stepTimes[this.currentStep].end = Date.now();
      this.stepTimes[this.currentStep].duration = 
        this.stepTimes[this.currentStep].end - this.stepTimes[this.currentStep].start;
    }
    this.currentStep = null;
  }

  getTotalTime() {
    return Date.now() - this.startTime;
  }

  getStepTime(stepName) {
    return this.stepTimes[stepName]?.duration || 0;
  }

  validatePerformance() {
    const totalTime = this.getTotalTime();
    const results = {
      testName: this.testName,
      totalTime,
      withinTarget: totalTime < PERFORMANCE_BENCHMARKS.FLOWS.complete,
      steps: {},
    };

    Object.keys(this.stepTimes).forEach(stepName => {
      const stepTime = this.getStepTime(stepName);
      const benchmark = PERFORMANCE_BENCHMARKS.STEPS[stepName];
      results.steps[stepName] = {
        time: stepTime,
        benchmark,
        withinTarget: benchmark ? stepTime < benchmark : true,
      };
    });

    return results;
  }

  logResults() {
    const results = this.validatePerformance();
    console.log(`\n📊 Performance Results for ${results.testName}:`);
    console.log(`⏱️  Total Time: ${results.totalTime}ms (Target: ${PERFORMANCE_BENCHMARKS.FLOWS.complete}ms)`);
    console.log(`🎯 Within Target: ${results.withinTarget ? '✅' : '❌'}`);
    
    Object.keys(results.steps).forEach(stepName => {
      const step = results.steps[stepName];
      console.log(`   ${stepName}: ${step.time}ms ${step.withinTarget ? '✅' : '❌'}`);
    });
  }
}

/**
 * Sequential flow validator
 * Ensures proper step completion and state persistence
 */
class FlowValidator {
  constructor() {
    this.completedSteps = [];
    this.stepData = {};
  }

  markStepComplete(stepName, data = {}) {
    this.completedSteps.push(stepName);
    this.stepData[stepName] = { ...data, completedAt: Date.now() };
  }

  validateStepSequence(requiredSteps) {
    const missingSteps = requiredSteps.filter(step => !this.completedSteps.includes(step));
    if (missingSteps.length > 0) {
      throw new Error(`Missing required steps: ${missingSteps.join(', ')}`);
    }
    return true;
  }

  getStepData(stepName) {
    return this.stepData[stepName];
  }

  validateStateTransition(fromStep, toStep) {
    const validTransitions = {
      'address': ['service'],
      'service': ['datetime'],
      'datetime': ['confirmation'],
      'confirmation': ['success'],
    };

    const allowedNext = validTransitions[fromStep] || [];
    if (!allowedNext.includes(toStep)) {
      throw new Error(`Invalid state transition from ${fromStep} to ${toStep}`);
    }
    return true;
  }
}

/**
 * Enhanced booking flow utilities
 */
class BookingFlowUtils {
  constructor() {
    this.monitor = null;
    this.validator = new FlowValidator();
  }

  async initializeTest(testName, userData, locationData) {
    this.monitor = new PerformanceMonitor(testName);
    this.monitor.startStep('setup');

    await initData('resetData');
    await initData('user/createUser', [userData]);
    await initData('user/updateUser', {
      phone: userData.phone,
      isoCode: userData.isoCode,
      dataUpdate: {
        housekeepingLocations: [locationData],
      },
    });

    this.monitor.endStep();
    return this;
  }

  async completeAddressSelection(addressIndex = 1) {
    this.monitor.startStep('addressSelection');

    await tapIdService(TEST_IDS.SERVICE_ENTRY);
    await removeIntroHouseKeeping();
    
    // Use optimized scroll for address selection
    await bookingFlowScroll('address', TEST_IDS.ADDRESS_SCROLL, TEST_IDS.ADDRESS_ITEM(addressIndex));
    await tapId(TEST_IDS.ADDRESS_ITEM(addressIndex));
    
    // Validate navigation to service configuration
    await expectElementVisible(TEST_IDS.HOME_TYPE_CONTAINER);
    
    this.validator.markStepComplete('address', { selectedIndex: addressIndex });
    this.monitor.endStep();
    return this;
  }

  async completeServiceConfiguration(homeType = 'HOTEL', roomType = 'SINGLE', quantity = 1) {
    this.validator.validateStepSequence(['address']);
    this.monitor.startStep('serviceConfig');

    // Select home type
    await bookingFlowScroll('service', TEST_IDS.HOME_TYPE_CONTAINER, TEST_IDS.HOME_TYPE_BUTTON(homeType));
    await tapId(TEST_IDS.HOME_TYPE_BUTTON(homeType));
    
    // Configure room type and quantity
    await scrollToRevealElement(
      TEST_IDS.ROOM_TYPE_CONTAINER,
      TEST_IDS.ROOM_INCREMENT(roomType),
      'down',
      SCROLL_CONFIG.SCREENS.service.maxAttempts,
      SCROLL_CONFIG.SCREENS.service.increment
    );
    
    // Add rooms based on quantity
    for (let i = 0; i < quantity; i++) {
      await tapId(TEST_IDS.ROOM_INCREMENT(roomType));
    }
    
    // Proceed to next step
    await expectElementVisible(TEST_IDS.NEXT_STEP);
    await tapId(TEST_IDS.NEXT_STEP);
    
    // Validate navigation to date/time selection
    await expectElementVisible(TEST_IDS.DATE_PICKER);
    
    this.validator.markStepComplete('service', { homeType, roomType, quantity });
    this.monitor.endStep();
    return this;
  }

  async completeDateTimeSelection(note = null) {
    this.validator.validateStepSequence(['address', 'service']);
    this.monitor.startStep('dateTimeSelection');

    // Interact with date picker
    await bookingFlowScroll('datetime', TEST_IDS.DATE_PICKER, TEST_IDS.DATE_PICKER);
    
    // Interact with time picker
    await bookingFlowScroll('datetime', TEST_IDS.TIME_PICKER, TEST_IDS.TIME_PICKER);
    
    // Add note if provided
    if (note) {
      await scrollToRevealElement(
        TEST_IDS.NOTE_INPUT,
        TEST_IDS.NOTE_INPUT,
        'down',
        SCROLL_CONFIG.SCREENS.datetime.maxAttempts,
        SCROLL_CONFIG.SCREENS.datetime.increment
      );
      await typeToTextField(TEST_IDS.NOTE_INPUT, note);
    }
    
    // Proceed to confirmation
    await expectElementVisible(TEST_IDS.NEXT_STEP);
    await tapId(TEST_IDS.NEXT_STEP);
    
    // Validate navigation to confirmation
    await expectElementVisible(TEST_IDS.CONFIRMATION_SCROLL);
    
    this.validator.markStepComplete('datetime', { note });
    this.monitor.endStep();
    return this;
  }

  async completePaymentConfirmation() {
    this.validator.validateStepSequence(['address', 'service', 'datetime']);
    this.monitor.startStep('confirmation');

    // Navigate through confirmation sections
    await confirmationScreenScroll(TEST_IDS.CONFIRMATION_SCROLL, 'location');
    await expectElementVisible(TEST_IDS.LOCATION_CONTAINER);
    
    await confirmationScreenScroll(TEST_IDS.CONFIRMATION_SCROLL, 'task-detail');
    await expectElementVisible(TEST_IDS.TASK_DETAIL_CONTAINER);
    
    await confirmationScreenScroll(TEST_IDS.CONFIRMATION_SCROLL, 'payment');
    await expectElementVisible(TEST_IDS.PAYMENT_METHOD_CONTAINER);
    
    // Submit booking
    await confirmationScreenScroll(TEST_IDS.CONFIRMATION_SCROLL, 'submit');
    await expectElementVisible(TEST_IDS.SUBMIT_BOOKING);
    await tapId(TEST_IDS.SUBMIT_BOOKING);
    
    // Validate booking success
    await waitForElement(TEST_IDS.SUCCESS_TEXT, TIMEOUTS.BOOKING_SUCCESS, 'text');
    
    this.validator.markStepComplete('confirmation');
    this.monitor.endStep();
    return this;
  }

  async executeCompleteFlow(config = {}) {
    const {
      homeType = 'HOTEL',
      roomType = 'SINGLE',
      quantity = 1,
      note = null,
      addressIndex = 1,
    } = config;

    await this.completeAddressSelection(addressIndex);
    await this.completeServiceConfiguration(homeType, roomType, quantity);
    await this.completeDateTimeSelection(note);
    await this.completePaymentConfirmation();
    
    return this.getResults();
  }

  async executeRapidFlow(config = {}) {
    // Use rapid scroll configurations for performance
    const originalConfig = { ...SCROLL_CONFIG.STANDARD };
    Object.assign(SCROLL_CONFIG.STANDARD, SCROLL_CONFIG.RAPID);
    
    try {
      const results = await this.executeCompleteFlow(config);
      return results;
    } finally {
      // Restore original scroll configuration
      Object.assign(SCROLL_CONFIG.STANDARD, originalConfig);
    }
  }

  getResults() {
    const performanceResults = this.monitor.validatePerformance();
    const flowData = this.validator.stepData;
    
    return {
      performance: performanceResults,
      flow: flowData,
      success: performanceResults.withinTarget,
    };
  }

  logResults() {
    if (this.monitor) {
      this.monitor.logResults();
    }
  }
}

/**
 * Error handling utilities
 */
class ErrorHandler {
  static async withRetry(operation, maxRetries = ERROR_CONFIG.RETRIES.elementFind) {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        console.log(`Attempt ${attempt}/${maxRetries} failed: ${error.message}`);
        
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }
    
    throw lastError;
  }

  static async withFallback(primaryOperation, fallbackOperation) {
    try {
      return await primaryOperation();
    } catch (error) {
      console.log(`Primary operation failed, trying fallback: ${error.message}`);
      return await fallbackOperation();
    }
  }
}

/**
 * Validation utilities
 */
class ValidationUtils {
  static async validateElementSequence(containerId, elementIds) {
    const results = [];
    
    for (const elementId of elementIds) {
      try {
        await scrollToRevealElement(containerId, elementId);
        await expectElementVisible(elementId);
        results.push({ elementId, accessible: true });
      } catch (error) {
        results.push({ elementId, accessible: false, error: error.message });
      }
    }
    
    return results;
  }

  static async validatePerformanceTarget(operation, targetTime) {
    const startTime = Date.now();
    await operation();
    const executionTime = Date.now() - startTime;
    
    return {
      executionTime,
      targetTime,
      withinTarget: executionTime < targetTime,
      efficiency: (targetTime - executionTime) / targetTime,
    };
  }
}

module.exports = {
  PerformanceMonitor,
  FlowValidator,
  BookingFlowUtils,
  ErrorHandler,
  ValidationUtils,
};
