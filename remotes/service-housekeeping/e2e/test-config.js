/**
 * Housekeeping E2E Test Configuration
 * 
 * Centralized configuration for test data, timeouts, and environment settings
 * optimized for the sequential booking flow requirements.
 */

// Test execution timeouts (in milliseconds)
const TIMEOUTS = {
  // Element visibility timeouts
  ELEMENT_VISIBLE: 2000,
  ELEMENT_VISIBLE_FAST: 1000,
  ELEMENT_VISIBLE_SLOW: 5000,
  
  // Navigation timeouts
  NAVIGATION: 3000,
  SCREEN_LOAD: 5000,
  
  // Scroll operation timeouts
  SCROLL_ATTEMPT: 1000,
  SCROLL_SETTLE: 200,
  SCROLL_RAPID: 100,
  
  // Performance test limits
  FULL_FLOW_MAX: 5 * 60 * 1000, // 5 minutes
  STEP_MAX: 60 * 1000,          // 1 minute per step
  RAPID_FLOW_MAX: 3 * 60 * 1000, // 3 minutes for optimized flow
  
  // Booking completion
  BOOKING_SUCCESS: 10000,
};

// Scroll configuration parameters
const SCROLL_CONFIG = {
  // Standard incremental scrolling
  STANDARD: {
    increment: 150,
    maxAttempts: 10,
    settleTime: 200,
  },
  
  // Performance-optimized scrolling
  RAPID: {
    increment: 300,
    maxAttempts: 5,
    settleTime: 100,
  },
  
  // Conservative scrolling for complex layouts
  CONSERVATIVE: {
    increment: 100,
    maxAttempts: 15,
    settleTime: 300,
  },
  
  // Screen-specific configurations
  SCREENS: {
    address: { increment: 100, maxAttempts: 5 },
    service: { increment: 150, maxAttempts: 8 },
    datetime: { increment: 150, maxAttempts: 6 },
    confirmation: { increment: 200, maxAttempts: 10 },
  },
};

// Test user data templates
const TEST_USERS = {
  ASKER: {
    isoCode: 'VN',
    phone: '0834567890',
    name: 'E2E Test Asker',
    type: 'ASKER',
    status: 'ACTIVE',
    oldUser: true,
  },
  
  TASKER: {
    isoCode: 'VN',
    phone: '0834567891',
    name: 'E2E Test Tasker',
    type: 'TASKER',
    status: 'ACTIVE',
    oldUser: true,
  },
  
  // Performance test user with minimal data
  PERFORMANCE_ASKER: {
    isoCode: 'VN',
    phone: '0834567892',
    name: 'Performance Test',
    type: 'ASKER',
    status: 'ACTIVE',
    oldUser: true,
  },
};

// Test location data templates
const TEST_LOCATIONS = {
  STANDARD: {
    country: 'VN',
    isoCode: 'VN',
    phoneNumber: '0834567890',
    address: 'Công ty TNHH bTaskee, Hẻm 284/25 Lý Thường Kiệt, phường 14, Quận 10, Hồ Chí Minh, Việt Nam',
    district: 'Quận 10',
    homeType: 'HOME',
    locationName: 'E2E Test Location',
    contact: 'E2E Test Asker',
    countryCode: '+84',
    lat: 10.7331278,
    lng: 106.706233,
    shortAddress: '284/25 Lý Thường Kiệt',
    _id: 'e2e_test_location_001',
    city: 'Hồ Chí Minh',
    description: 'Standard E2E test location',
  },
  
  PERFORMANCE: {
    country: 'VN',
    isoCode: 'VN',
    phoneNumber: '0834567892',
    address: 'Performance Test Address',
    district: 'Test District',
    homeType: 'HOME',
    locationName: 'Performance Test',
    contact: 'Performance Test',
    countryCode: '+84',
    lat: 10.7331278,
    lng: 106.706233,
    shortAddress: 'Performance Addr',
    _id: 'performance_test_location',
    city: 'Hồ Chí Minh',
    description: 'Performance test location',
  },
  
  SCROLL_TEST: {
    country: 'VN',
    isoCode: 'VN',
    phoneNumber: '0834567890',
    address: 'Scroll Test Address with Very Long Name for Testing Viewport Management',
    district: 'Scroll Test District',
    homeType: 'HOME',
    locationName: 'Scroll Test Location',
    contact: 'Scroll Test User',
    countryCode: '+84',
    lat: 10.7331278,
    lng: 106.706233,
    shortAddress: 'Scroll Test Addr',
    _id: 'scroll_test_location',
    city: 'Hồ Chí Minh',
    description: 'Location for scroll testing',
  },
};

// Service configuration options
const SERVICE_CONFIG = {
  HOME_TYPES: {
    HOTEL: 'HOTEL',
    VILLA: 'VILLA',
    APARTMENT: 'APARTMENT',
  },
  
  ROOM_TYPES: {
    SINGLE: 'SINGLE',
    DOUBLE: 'DOUBLE',
    TRIPLE: 'TRIPLE',
    LIVING_ROOM: 'LIVING_ROOM',
    KITCHEN: 'KITCHEN',
    BATHROOM: 'BATHROOM',
  },
  
  DEFAULT_SELECTIONS: {
    homeType: 'HOTEL',
    roomType: 'SINGLE',
    quantity: 1,
    roomNumber: '101',
  },
};

// TestID patterns and selectors
const TEST_IDS = {
  // Navigation and flow
  SERVICE_ENTRY: 'postTaskServiceHOUSE_KEEPING',
  NEXT_STEP: 'btnNextStep3',
  SUBMIT_BOOKING: 'btnSubmitPostTask',
  
  // Address selection
  ADDRESS_SCROLL: 'scrollChooseAddress',
  ADDRESS_ITEM: (index) => `address${index}`,
  ADD_LOCATION: 'btnAddNewLocation',
  EDIT_ADDRESS: 'edit-address-button',
  
  // Service configuration
  HOME_TYPE_CONTAINER: 'home-type-selection-container',
  ROOM_TYPE_CONTAINER: 'room-type-selection-container',
  HOME_TYPE_BUTTON: (type) => `btnHostelType_${type}`,
  ROOM_INCREMENT: (room) => `btnIncrement_${room}`,
  ROOM_DECREMENT: (room) => `btnMinus_${room}`,
  ROOM_NUMBER_INPUT: 'room-number-input',
  WORKING_PROCESS: 'working-process-button',
  
  // Date/time selection
  DATE_PICKER: 'date-picker-component',
  TIME_PICKER: 'time-picker-component',
  NOTE_INPUT: 'note-input-component',
  
  // Confirmation/payment
  CONFIRMATION_SCROLL: 'scrollViewStep4',
  LOCATION_CONTAINER: 'location-post-task-container',
  TASK_DETAIL_CONTAINER: 'task-detail-container',
  PAYMENT_DETAIL_CONTAINER: 'payment-detail-container',
  PAYMENT_METHOD_CONTAINER: 'payment-method-container',
  TASK_DETAIL_HEADER: 'txtTaskDetail',
  
  // Success indicators
  SUCCESS_TEXT: 'Đăng việc thành công',
  TRACK_TASK_TEXT: 'Theo dõi công việc',
};

// Performance benchmarks
const PERFORMANCE_BENCHMARKS = {
  // Step-by-step timing targets (in milliseconds)
  STEPS: {
    setup: 10000,           // 10 seconds
    addressSelection: 15000, // 15 seconds
    serviceConfig: 45000,    // 45 seconds
    dateTimeSelection: 30000, // 30 seconds
    confirmation: 25000,     // 25 seconds
    navigation: 35000,       // 35 seconds total
  },
  
  // Flow completion targets
  FLOWS: {
    complete: 5 * 60 * 1000,    // 5 minutes
    optimized: 3 * 60 * 1000,   // 3 minutes
    rapid: 2 * 60 * 1000,       // 2 minutes
  },
  
  // Scroll performance targets
  SCROLL: {
    elementReveal: 5000,     // 5 seconds to reveal any element
    sectionNavigation: 2000, // 2 seconds to navigate between sections
    formProgression: 10000,  // 10 seconds to scroll through entire form
  },
};

// Error handling configuration
const ERROR_CONFIG = {
  // Retry attempts for different operations
  RETRIES: {
    elementFind: 3,
    scroll: 5,
    navigation: 2,
    input: 3,
  },
  
  // Error messages
  MESSAGES: {
    ELEMENT_NOT_FOUND: 'Element not found after maximum scroll attempts',
    NAVIGATION_FAILED: 'Navigation to next step failed',
    SCROLL_TIMEOUT: 'Scroll operation timed out',
    FLOW_INCOMPLETE: 'Sequential flow validation failed',
  },
  
  // Fallback strategies
  FALLBACKS: {
    useAlternativeScroll: true,
    skipNonCriticalSteps: false,
    enableDebugLogging: true,
  },
};

// Test environment configuration
const ENVIRONMENT = {
  // Device settings
  DEVICE: {
    permissions: {
      notifications: 'YES',
      userTracking: 'YES',
      location: 'always',
      camera: 'YES',
      medialibrary: 'YES',
      photos: 'YES',
    },
    launchArgs: {
      isE2ETesting: true,
      initialRouteName: 'HouseKeepingService',
      detoxDebugSynchronization: 1000,
      isoCode: 'VN',
    },
  },
  
  // Test data reset
  RESET: {
    beforeEach: true,
    afterAll: true,
    preserveUser: false,
  },
};

module.exports = {
  TIMEOUTS,
  SCROLL_CONFIG,
  TEST_USERS,
  TEST_LOCATIONS,
  SERVICE_CONFIG,
  TEST_IDS,
  PERFORMANCE_BENCHMARKS,
  ERROR_CONFIG,
  ENVIRONMENT,
};
