/**
 * Optimized Housekeeping E2E Tests with Advanced Scroll Management
 * 
 * Demonstrates enhanced scroll-to-reveal patterns with:
 * - Incremental scrolling (150px increments)
 * - Element visibility validation
 * - Performance optimization for 3-5 minute execution
 * - Comprehensive viewport management
 */

const {
  initData,
  tapId,
  waitForElement,
  expectElementVisible,
  typeToTextField,
  removeIntroHouseKeeping,
  tapIdService,
} = require('./step-definition');

const {
  scrollToRevealElement,
  bookingFlowScroll,
  confirmationScreenScroll,
  roomTypeScroll,
  rapidScroll,
  validateScrollSequence,
} = require('./scroll-helpers');

const { E2EHelpers } = require('../../../e2e/e2e.helpers');

// Test data
const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Scroll Test User',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const TEST_LOCATION = {
  country: 'VN',
  isoCode: 'VN',
  phoneNumber: '0834567890',
  address: 'Test Address for Scroll Validation',
  district: 'Test District',
  homeType: 'HOME',
  locationName: 'Scroll Test Location',
  contact: 'Scroll Test User',
  countryCode: '+84',
  lat: 10.7331278,
  lng: 106.706233,
  shortAddress: 'Test Short Address',
  _id: 'scroll_test_location_001',
  city: 'Hồ Chí Minh',
  description: 'Location for scroll testing',
};

describe('Housekeeping Service - Optimized Scroll Management E2E', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await initData('user/updateUser', {
      phone: ASKER.phone,
      isoCode: ASKER.isoCode,
      dataUpdate: {
        housekeepingLocations: [TEST_LOCATION],
      },
    });
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  describe('Address Selection with Scroll Optimization', () => {
    it('should handle address list scrolling efficiently', async () => {
      await tapIdService('postTaskServiceHOUSE_KEEPING');
      await removeIntroHouseKeeping();
      
      // Use optimized scroll for address selection
      await bookingFlowScroll('address', 'scrollChooseAddress', 'address1');
      await tapId('address1');
      
      // Verify successful navigation
      await expectElementVisible('home-type-selection-container');
    });

    it('should validate scroll sequence for address elements', async () => {
      await tapIdService('postTaskServiceHOUSE_KEEPING');
      await removeIntroHouseKeeping();
      
      // Validate all address-related elements are accessible
      const addressElements = ['address1', 'btnAddNewLocation'];
      const results = await validateScrollSequence('scrollChooseAddress', addressElements);
      
      // Verify all elements are accessible
      results.forEach(result => {
        expect(result.accessible).toBe(true);
      });
    });
  });

  describe('Service Configuration with Advanced Scrolling', () => {
    beforeEach(async () => {
      await tapIdService('postTaskServiceHOUSE_KEEPING');
      await removeIntroHouseKeeping();
      await tapId('address1');
    });

    it('should handle home type selection with scroll optimization', async () => {
      // Use booking flow scroll for service configuration
      await bookingFlowScroll('service', 'home-type-selection-container', 'btnHostelType_HOTEL');
      await tapId('btnHostelType_HOTEL');
      
      // Verify home type is selected
      await expectElementVisible('room-type-selection-container');
    });

    it('should manage room type scrolling with quantity controls', async () => {
      await tapId('btnHostelType_HOTEL');
      
      // Use specialized room type scroll
      await roomTypeScroll('room-type-selection-container', 'SINGLE');
      await tapId('btnIncrement_SINGLE');
      
      // Verify room quantity is updated
      await expectElementVisible('btnNextStep3');
    });

    it('should handle room number input with scroll-to-reveal', async () => {
      await tapId('btnHostelType_HOTEL');
      await tapId('btnIncrement_SINGLE');
      
      // Scroll to reveal room number input
      await scrollToRevealElement(
        'room-type-selection-container',
        'room-number-input',
        'down',
        6,
        150
      );
      
      await typeToTextField('room-number-input', '301');
      await expectElementVisible('btnNextStep3');
    });

    it('should validate complete service configuration scroll sequence', async () => {
      const serviceElements = [
        'btnHostelType_HOTEL',
        'btnIncrement_SINGLE',
        'room-number-input',
        'working-process-button',
        'btnNextStep3'
      ];
      
      // Validate all service elements are accessible via scrolling
      const results = await validateScrollSequence('room-type-selection-container', serviceElements);
      
      // Check accessibility results
      const accessibleElements = results.filter(r => r.accessible).length;
      expect(accessibleElements).toBeGreaterThan(3); // At least most elements should be accessible
    });
  });

  describe('Date/Time Selection with Scroll Management', () => {
    beforeEach(async () => {
      await tapIdService('postTaskServiceHOUSE_KEEPING');
      await removeIntroHouseKeeping();
      await tapId('address1');
      await tapId('btnHostelType_HOTEL');
      await tapId('btnIncrement_SINGLE');
      await tapId('btnNextStep3');
    });

    it('should handle date/time picker scrolling', async () => {
      // Use booking flow scroll for datetime components
      await bookingFlowScroll('datetime', 'date-picker-component', 'date-picker-component');
      await expectElementVisible('date-picker-component');
      
      await bookingFlowScroll('datetime', 'time-picker-component', 'time-picker-component');
      await expectElementVisible('time-picker-component');
    });

    it('should manage note input with scroll optimization', async () => {
      // Scroll to reveal note input
      await scrollToRevealElement(
        'note-input-component',
        'note-input-component',
        'down',
        5,
        150
      );
      
      await typeToTextField('note-input-component', 'Optimized scroll test note');
      await expectElementVisible('btnNextStep3');
    });

    it('should use rapid scroll for performance testing', async () => {
      const startTime = Date.now();
      
      // Use rapid scroll for faster execution
      await rapidScroll('date-picker-component', 'date-picker-component');
      await rapidScroll('time-picker-component', 'time-picker-component');
      await rapidScroll('note-input-component', 'note-input-component');
      
      const scrollTime = Date.now() - startTime;
      expect(scrollTime).toBeLessThan(5000); // Should complete within 5 seconds
      
      await tapId('btnNextStep3');
      await expectElementVisible('scrollViewStep4');
    });
  });

  describe('Confirmation Screen with Comprehensive Scroll Patterns', () => {
    beforeEach(async () => {
      await tapIdService('postTaskServiceHOUSE_KEEPING');
      await removeIntroHouseKeeping();
      await tapId('address1');
      await tapId('btnHostelType_HOTEL');
      await tapId('btnIncrement_SINGLE');
      await tapId('btnNextStep3');
      await tapId('btnNextStep3');
    });

    it('should navigate confirmation screen sections with optimized scrolling', async () => {
      // Test each section of confirmation screen
      await confirmationScreenScroll('scrollViewStep4', 'location');
      await expectElementVisible('location-post-task-container');
      
      await confirmationScreenScroll('scrollViewStep4', 'task-detail');
      await expectElementVisible('task-detail-container');
      
      await confirmationScreenScroll('scrollViewStep4', 'payment');
      await expectElementVisible('payment-method-container');
      
      await confirmationScreenScroll('scrollViewStep4', 'submit');
      await expectElementVisible('btnSubmitPostTask');
    });

    it('should validate all confirmation elements accessibility', async () => {
      const confirmationElements = [
        'location-post-task-container',
        'task-detail-container',
        'payment-detail-container',
        'payment-method-container',
        'btnSubmitPostTask'
      ];
      
      const results = await validateScrollSequence('scrollViewStep4', confirmationElements);
      
      // Verify high accessibility rate
      const accessibleCount = results.filter(r => r.accessible).length;
      expect(accessibleCount).toBeGreaterThanOrEqual(4);
    });

    it('should complete booking with optimized scroll performance', async () => {
      const startTime = Date.now();
      
      // Use rapid scroll to submit button
      await rapidScroll('scrollViewStep4', 'btnSubmitPostTask');
      await tapId('btnSubmitPostTask');
      
      await waitForElement('Đăng việc thành công', 5000, 'text');
      
      const totalTime = Date.now() - startTime;
      expect(totalTime).toBeLessThan(10000); // Should complete within 10 seconds
    });
  });

  describe('End-to-End Flow with Performance Optimization', () => {
    it('should complete full booking flow with optimized scrolling under 3 minutes', async () => {
      const startTime = Date.now();
      
      // Execute complete flow with optimized scroll patterns
      await tapIdService('postTaskServiceHOUSE_KEEPING');
      await removeIntroHouseKeeping();
      
      // Address selection
      await rapidScroll('scrollChooseAddress', 'address1');
      await tapId('address1');
      
      // Service configuration
      await rapidScroll('home-type-selection-container', 'btnHostelType_HOTEL');
      await tapId('btnHostelType_HOTEL');
      await roomTypeScroll('room-type-selection-container', 'SINGLE');
      await tapId('btnIncrement_SINGLE');
      await tapId('btnNextStep3');
      
      // Date/time selection
      await rapidScroll('date-picker-component', 'btnNextStep3');
      await tapId('btnNextStep3');
      
      // Confirmation and booking
      await rapidScroll('scrollViewStep4', 'btnSubmitPostTask');
      await tapId('btnSubmitPostTask');
      
      await waitForElement('Đăng việc thành công', 5000, 'text');
      
      const executionTime = Date.now() - startTime;
      const maxTime = 3 * 60 * 1000; // 3 minutes
      
      expect(executionTime).toBeLessThan(maxTime);
      console.log(`Optimized flow completed in ${executionTime}ms`);
    });
  });
});
