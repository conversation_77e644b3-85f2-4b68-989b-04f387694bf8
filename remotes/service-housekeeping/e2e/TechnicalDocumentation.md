# Housekeeping Service E2E Testing - Technical Documentation

## 🏗️ **Architecture Overview**

### 📁 **File Structure**
```
remotes/service-housekeeping/e2e/
├── HousekeepingServiceBooking.e2e.js     # Main comprehensive test suite
├── HousekeepingScrollOptimized.e2e.js    # Scroll-optimized performance tests
├── scroll-helpers.js                     # Advanced scroll management utilities
├── step-definition.js                    # Base test utilities (existing)
├── setup.ts                             # Test environment setup
├── TestCoverageReport.md                 # Executive coverage summary
└── TechnicalDocumentation.md             # This technical guide
```

### 🔧 **Core Components**
- **Test Suites**: Comprehensive E2E tests following sequential booking flow
- **Scroll Helpers**: Advanced viewport management and scroll-to-reveal patterns
- **TestID Enhancements**: Semantic testID additions for reliable element selection
- **Performance Optimization**: 3-5 minute execution time targeting

## 🎯 **Sequential Flow Patterns**

### 📋 **Mandatory Flow Sequence**
```javascript
// REQUIRED: Complete each step before proceeding to next
Address Selection → Service Configuration → Date/Time Selection → Payment/Confirmation
```

### 🔄 **Flow Implementation Pattern**
```javascript
describe('Sequential Flow Test', () => {
  beforeEach(async () => {
    // Reset data and initialize environment
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  it('should complete sequential booking flow', async () => {
    // Step 1: Address Selection (REQUIRED)
    await tapIdService('postTaskServiceHOUSE_KEEPING');
    await removeIntroHouseKeeping();
    await tapId('address1');
    
    // Step 2: Service Configuration (REQUIRED)
    await tapId('btnHostelType_HOTEL');
    await tapId('btnIncrement_SINGLE');
    await tapId('btnNextStep3');
    
    // Step 3: Date/Time Selection (REQUIRED)
    await tapId('btnNextStep3');
    
    // Step 4: Payment/Confirmation (REQUIRED)
    await tapId('btnSubmitPostTask');
    await waitForElement('Đăng việc thành công', 5000, 'text');
  });
});
```

## 🏷️ **TestID Mapping Reference**

### 📍 **Address Selection Screen**
```javascript
// Container Elements
'scrollChooseAddress'           // Main address list container

// Interactive Elements  
'address1', 'address2', ...     // Location selection items
'btnAddNewLocation'             // Add new location button
'btnEditLocation_{address}'     // Edit location options
```

### ⚙️ **Service Configuration Screen**
```javascript
// Container Elements
'home-type-selection-container' // Home type selection wrapper
'room-type-selection-container' // Room configuration wrapper

// Home Type Selection
'btnHostelType_HOTEL'          // Hotel type button
'btnHostelType_VILLA'          // Villa type button  
'btnHostelType_APARTMENT'      // Apartment type button

// Room Configuration
'btnIncrement_{roomType}'      // Room quantity increment (e.g., 'btnIncrement_SINGLE')
'btnMinus_{roomType}'          // Room quantity decrement
'txtAmount'                    // Quantity display
'room-number-input'            // Room number input field

// Navigation
'edit-address-button'          // Edit address functionality
'working-process-button'       // Working process navigation
'btnNextStep3'                 // Proceed to next step
```

### 📅 **Date/Time Selection Screen**
```javascript
// Date/Time Components
'date-picker-component'        // Date selection component
'time-picker-component'        // Time selection component

// Input Elements
'note-input-component'         // Note for tasker input

// Navigation
'btnNextStep3'                 // Proceed to confirmation
```

### 💳 **Confirmation/Payment Screen**
```javascript
// Container Elements
'scrollViewStep4'              // Main confirmation scroll container
'location-post-task-container' // Location details wrapper
'task-detail-container'        // Task summary wrapper
'payment-detail-container'     // Payment information wrapper
'payment-method-container'     // Payment method selection wrapper

// Content Elements
'txtTaskDetail'                // Task detail header
'taskDetail_{value}'           // Task detail values
'amount_{value}'               // Amount displays

// Actions
'btnSubmitPostTask'            // Final booking submission
```

## 🔄 **Scroll Management Patterns**

### 📏 **Incremental Scrolling (Recommended)**
```javascript
// 150px increments for reliable element access
const scrollToRevealElement = async (containerId, targetElementId) => {
  const scrollIncrement = 150;
  const maxAttempts = 10;
  
  for (let attempts = 0; attempts < maxAttempts; attempts++) {
    try {
      await waitFor(element(by.id(targetElementId)))
        .toBeVisible()
        .withTimeout(1000);
      return; // Element found
    } catch (error) {
      await element(by.id(containerId)).scroll(scrollIncrement, 'down');
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  }
};
```

### 🎯 **Booking Flow Scroll Configuration**
```javascript
const scrollConfigs = {
  address: { increment: 100, maxAttempts: 5 },      // Simple list scrolling
  service: { increment: 150, maxAttempts: 8 },      // Form with controls
  datetime: { increment: 150, maxAttempts: 6 },     // Picker components
  confirmation: { increment: 200, maxAttempts: 10 } // Complex layout
};
```

### 🚀 **Performance-Optimized Scrolling**
```javascript
// For performance tests - larger increments, shorter timeouts
const rapidScroll = async (containerId, targetElementId) => {
  const rapidScrollIncrement = 300;
  const maxAttempts = 5;
  
  for (let attempts = 0; attempts < maxAttempts; attempts++) {
    try {
      await waitFor(element(by.id(targetElementId)))
        .toBeVisible()
        .withTimeout(500);
      return;
    } catch (error) {
      await element(by.id(containerId)).scroll(rapidScrollIncrement, 'down');
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
};
```

## ⚡ **Performance Optimization Strategies**

### 🎯 **3-5 Minute Execution Target**
```javascript
// Performance test pattern
it('should complete full booking flow within 3-5 minutes', async () => {
  const startTime = Date.now();
  
  // Execute optimized flow
  await rapidBookingFlow();
  
  const executionTime = Date.now() - startTime;
  const maxExecutionTime = 5 * 60 * 1000; // 5 minutes
  
  expect(executionTime).toBeLessThan(maxExecutionTime);
});
```

### 📊 **Performance Breakdown**
- **Setup/Login**: ~10 seconds
- **Address Selection**: ~15 seconds  
- **Service Configuration**: ~45 seconds
- **Date/Time Selection**: ~30 seconds
- **Confirmation/Payment**: ~25 seconds
- **Navigation/Transitions**: ~35 seconds

### 🚀 **Optimization Techniques**
1. **Rapid Scroll Mode**: 300px increments for performance tests
2. **Parallel Validations**: Concurrent element checks
3. **Smart Timeouts**: Reduced wait times for known fast operations
4. **Batch Operations**: Group related interactions

## 🛠️ **Setup Instructions**

### 📋 **Prerequisites**
```bash
# Install dependencies
pnpm install

# Ensure Detox is configured
npx detox build --configuration ios.sim.debug
```

### 🚀 **Running Tests**
```bash
# Run comprehensive test suite
npx detox test remotes/service-housekeeping/e2e/HousekeepingServiceBooking.e2e.js

# Run scroll-optimized tests
npx detox test remotes/service-housekeeping/e2e/HousekeepingScrollOptimized.e2e.js

# Run specific test case
npx detox test -t "should complete sequential booking flow"
```

### ⚙️ **Configuration**
```javascript
// In setup.ts
beforeAll(async () => {
  await device.launchApp({
    permissions: {
      notifications: 'YES',
      location: 'always',
      camera: 'YES',
    },
    launchArgs: {
      isE2ETesting: true,
      initialRouteName: 'HouseKeepingService',
      isoCode: 'VN',
    },
  });
});
```

## 🔍 **Debugging and Troubleshooting**

### 🐛 **Common Issues**

#### Element Not Found
```javascript
// Add debug logging
try {
  await element(by.id('targetElement')).tap();
} catch (error) {
  console.log('Element not found, attempting scroll...');
  await scrollToRevealElement('container', 'targetElement');
  await element(by.id('targetElement')).tap();
}
```

#### Scroll Timeout
```javascript
// Increase scroll attempts for complex layouts
await scrollToRevealElement(
  'container',
  'targetElement', 
  'down',
  15, // Increased from default 10
  150
);
```

#### Performance Issues
```javascript
// Use rapid scroll for performance tests
await rapidScroll('container', 'targetElement');

// Or reduce wait times
await waitFor(element(by.id('element')))
  .toBeVisible()
  .withTimeout(500); // Reduced from 1000ms
```

### 📊 **Monitoring and Metrics**
```javascript
// Add performance monitoring
const startTime = Date.now();
await executeTestStep();
const stepTime = Date.now() - startTime;
console.log(`Step completed in ${stepTime}ms`);
```

## 📋 **Best Practices**

### ✅ **Required Practices**
1. **Always use testID selectors** - Never use text-based selectors
2. **Follow sequential flow** - Complete each step before proceeding
3. **Implement scroll-to-reveal** - Handle viewport limitations
4. **Add proper waits** - Use `waitFor` with appropriate timeouts
5. **Reset data between tests** - Use `initData('resetData')`

### 🎯 **Recommended Patterns**
```javascript
// Good: TestID-based selection with scroll
await scrollToRevealElement('container', 'btnSubmit');
await tapId('btnSubmit');

// Bad: Text-based selection
await tapText('Submit'); // ❌ Never use this

// Good: Sequential flow validation
await completeAddressSelection();
await completeServiceConfiguration();
await completeDateTimeSelection();
await completePaymentConfirmation();

// Bad: Skipping steps
await tapId('btnSubmitPostTask'); // ❌ Without completing prerequisites
```

### 🔧 **Error Handling**
```javascript
// Implement graceful error handling
try {
  await scrollToRevealElement('container', 'element');
} catch (error) {
  console.error(`Failed to reveal element: ${error.message}`);
  // Implement fallback strategy
  await alternativeScrollPattern();
}
```

## 📚 **Additional Resources**

### 🔗 **Related Documentation**
- [Detox API Reference](https://github.com/wix/Detox/blob/master/docs/README.md)
- [React Native Testing Guide](https://reactnative.dev/docs/testing-overview)
- [TestID Best Practices](https://github.com/wix/Detox/blob/master/docs/Guide.WritingTests.md)

### 🛠️ **Utility Functions**
- `scroll-helpers.js` - Advanced scroll management
- `step-definition.js` - Base test utilities
- `e2e.helpers.js` - Common E2E patterns

### 📊 **Monitoring Tools**
- Performance timing logs
- Element accessibility validation
- Scroll pattern effectiveness metrics
- Test execution time tracking
