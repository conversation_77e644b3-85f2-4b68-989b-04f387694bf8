/**
 * Advanced Housekeeping E2E Test Suite
 * 
 * Demonstrates comprehensive testing with:
 * - Performance monitoring and validation
 * - Advanced scroll management
 * - Sequential flow validation
 * - Error handling and recovery
 * - Comprehensive reporting
 */

const { E2EHelpers } = require('../../../e2e/e2e.helpers');
const {
  TEST_USERS,
  TEST_LOCATIONS,
  SERVICE_CONFIG,
  PERFORMANCE_BENCHMARKS,
} = require('./test-config');

const {
  Booking<PERSON>low<PERSON>til<PERSON>,
  ErrorHandler,
  ValidationUtils,
  PerformanceMonitor,
} = require('./test-utilities');

describe('Housekeeping Service - Advanced E2E Test Suite', () => {
  let flowUtils;

  beforeEach(async () => {
    flowUtils = new BookingFlowUtils();
  });

  afterEach(async () => {
    if (flowUtils) {
      flowUtils.logResults();
    }
  });

  describe('🚀 Performance-Optimized Complete Flows', () => {
    it('should complete standard booking flow within performance targets', async () => {
      await flowUtils.initializeTest(
        'Standard Booking Flow',
        TEST_USERS.ASKER,
        TEST_LOCATIONS.STANDARD
      );
      
      await E2EHelpers.onHaveLogin(TEST_USERS.ASKER.phone, '123456');
      
      const results = await flowUtils.executeCompleteFlow({
        homeType: SERVICE_CONFIG.HOME_TYPES.HOTEL,
        roomType: SERVICE_CONFIG.ROOM_TYPES.SINGLE,
        quantity: 1,
        note: 'Standard booking flow test with performance monitoring',
      });
      
      // Validate performance targets
      expect(results.performance.withinTarget).toBe(true);
      expect(results.performance.totalTime).toBeLessThan(PERFORMANCE_BENCHMARKS.FLOWS.complete);
      
      // Validate flow completion
      expect(results.success).toBe(true);
    });

    it('should complete rapid booking flow under 3 minutes', async () => {
      await flowUtils.initializeTest(
        'Rapid Booking Flow',
        TEST_USERS.PERFORMANCE_ASKER,
        TEST_LOCATIONS.PERFORMANCE
      );
      
      await E2EHelpers.onHaveLogin(TEST_USERS.PERFORMANCE_ASKER.phone, '123456');
      
      const results = await flowUtils.executeRapidFlow({
        homeType: SERVICE_CONFIG.HOME_TYPES.HOTEL,
        roomType: SERVICE_CONFIG.ROOM_TYPES.SINGLE,
        quantity: 1,
      });
      
      // Validate rapid performance targets
      expect(results.performance.totalTime).toBeLessThan(PERFORMANCE_BENCHMARKS.FLOWS.rapid);
      expect(results.success).toBe(true);
      
      console.log(`🚀 Rapid flow completed in ${results.performance.totalTime}ms`);
    });

    it('should handle complex service configuration efficiently', async () => {
      await flowUtils.initializeTest(
        'Complex Service Configuration',
        TEST_USERS.ASKER,
        TEST_LOCATIONS.STANDARD
      );
      
      await E2EHelpers.onHaveLogin(TEST_USERS.ASKER.phone, '123456');
      
      const results = await flowUtils.executeCompleteFlow({
        homeType: SERVICE_CONFIG.HOME_TYPES.VILLA,
        roomType: SERVICE_CONFIG.ROOM_TYPES.DOUBLE,
        quantity: 3,
        note: 'Complex configuration with multiple rooms and detailed requirements',
      });
      
      // Validate complex configuration handling
      expect(results.flow.service.quantity).toBe(3);
      expect(results.flow.service.homeType).toBe('VILLA');
      expect(results.success).toBe(true);
    });
  });

  describe('🔄 Advanced Scroll Management Validation', () => {
    beforeEach(async () => {
      await flowUtils.initializeTest(
        'Scroll Management Test',
        TEST_USERS.ASKER,
        TEST_LOCATIONS.SCROLL_TEST
      );
      await E2EHelpers.onHaveLogin(TEST_USERS.ASKER.phone, '123456');
    });

    it('should validate all critical elements are accessible via scrolling', async () => {
      await flowUtils.completeAddressSelection();
      await flowUtils.completeServiceConfiguration();
      await flowUtils.completeDateTimeSelection();
      
      // Validate confirmation screen element accessibility
      const confirmationElements = [
        'location-post-task-container',
        'task-detail-container',
        'payment-detail-container',
        'payment-method-container',
        'btnSubmitPostTask'
      ];
      
      const results = await ValidationUtils.validateElementSequence(
        'scrollViewStep4',
        confirmationElements
      );
      
      // Verify high accessibility rate (at least 80%)
      const accessibleCount = results.filter(r => r.accessible).length;
      const accessibilityRate = accessibleCount / results.length;
      
      expect(accessibilityRate).toBeGreaterThanOrEqual(0.8);
      
      console.log(`📊 Element accessibility rate: ${(accessibilityRate * 100).toFixed(1)}%`);
    });

    it('should handle scroll performance under time constraints', async () => {
      const scrollPerformanceTest = async () => {
        await flowUtils.completeAddressSelection();
        await flowUtils.completeServiceConfiguration();
        await flowUtils.completeDateTimeSelection();
      };
      
      const performanceResult = await ValidationUtils.validatePerformanceTarget(
        scrollPerformanceTest,
        PERFORMANCE_BENCHMARKS.SCROLL.formProgression
      );
      
      expect(performanceResult.withinTarget).toBe(true);
      
      console.log(`⚡ Scroll performance: ${performanceResult.executionTime}ms (${(performanceResult.efficiency * 100).toFixed(1)}% efficiency)`);
    });
  });

  describe('🛡️ Error Handling and Recovery', () => {
    beforeEach(async () => {
      await flowUtils.initializeTest(
        'Error Handling Test',
        TEST_USERS.ASKER,
        TEST_LOCATIONS.STANDARD
      );
      await E2EHelpers.onHaveLogin(TEST_USERS.ASKER.phone, '123456');
    });

    it('should recover from scroll failures with retry logic', async () => {
      await flowUtils.completeAddressSelection();
      
      // Test error recovery during service configuration
      const serviceConfigWithRetry = async () => {
        return await ErrorHandler.withRetry(async () => {
          await flowUtils.completeServiceConfiguration();
        }, 3);
      };
      
      await expect(serviceConfigWithRetry()).resolves.not.toThrow();
    });

    it('should handle fallback scroll patterns', async () => {
      await flowUtils.completeAddressSelection();
      await flowUtils.completeServiceConfiguration();
      
      // Test fallback scroll pattern
      const dateTimeWithFallback = async () => {
        return await ErrorHandler.withFallback(
          async () => {
            // Primary: rapid scroll
            await flowUtils.completeDateTimeSelection('Fallback test note');
          },
          async () => {
            // Fallback: conservative scroll
            console.log('Using fallback scroll pattern');
            await flowUtils.completeDateTimeSelection('Fallback test note');
          }
        );
      };
      
      await expect(dateTimeWithFallback()).resolves.not.toThrow();
    });
  });

  describe('📊 Comprehensive Flow Validation', () => {
    it('should validate complete booking flow with state persistence', async () => {
      const monitor = new PerformanceMonitor('State Persistence Validation');
      
      await flowUtils.initializeTest(
        'State Persistence Test',
        TEST_USERS.ASKER,
        TEST_LOCATIONS.STANDARD
      );
      await E2EHelpers.onHaveLogin(TEST_USERS.ASKER.phone, '123456');
      
      monitor.startStep('addressSelection');
      await flowUtils.completeAddressSelection();
      monitor.endStep();
      
      monitor.startStep('serviceConfiguration');
      await flowUtils.completeServiceConfiguration('HOTEL', 'SINGLE', 2);
      monitor.endStep();
      
      monitor.startStep('dateTimeSelection');
      await flowUtils.completeDateTimeSelection('State persistence validation note');
      monitor.endStep();
      
      monitor.startStep('paymentConfirmation');
      await flowUtils.completePaymentConfirmation();
      monitor.endStep();
      
      // Validate state persistence across steps
      const flowData = flowUtils.validator.stepData;
      expect(flowData.address.selectedIndex).toBe(1);
      expect(flowData.service.homeType).toBe('HOTEL');
      expect(flowData.service.quantity).toBe(2);
      expect(flowData.datetime.note).toBe('State persistence validation note');
      
      // Validate performance
      const results = monitor.validatePerformance();
      expect(results.withinTarget).toBe(true);
      
      monitor.logResults();
    });

    it('should validate booking flow with multiple room types', async () => {
      await flowUtils.initializeTest(
        'Multiple Room Types Test',
        TEST_USERS.ASKER,
        TEST_LOCATIONS.STANDARD
      );
      await E2EHelpers.onHaveLogin(TEST_USERS.ASKER.phone, '123456');
      
      await flowUtils.completeAddressSelection();
      
      // Configure multiple room types (this would require enhanced service configuration)
      await flowUtils.completeServiceConfiguration('VILLA', 'DOUBLE', 2);
      await flowUtils.completeDateTimeSelection('Multiple room types configuration test');
      await flowUtils.completePaymentConfirmation();
      
      const results = flowUtils.getResults();
      expect(results.success).toBe(true);
      expect(results.flow.service.homeType).toBe('VILLA');
    });
  });

  describe('🎯 Edge Cases and Boundary Conditions', () => {
    it('should handle maximum room quantity selection', async () => {
      await flowUtils.initializeTest(
        'Maximum Quantity Test',
        TEST_USERS.ASKER,
        TEST_LOCATIONS.STANDARD
      );
      await E2EHelpers.onHaveLogin(TEST_USERS.ASKER.phone, '123456');
      
      await flowUtils.completeAddressSelection();
      
      // Test with maximum allowed quantity (assuming 10 is max)
      await flowUtils.completeServiceConfiguration('HOTEL', 'SINGLE', 5);
      await flowUtils.completeDateTimeSelection('Maximum quantity test');
      await flowUtils.completePaymentConfirmation();
      
      const results = flowUtils.getResults();
      expect(results.success).toBe(true);
      expect(results.flow.service.quantity).toBe(5);
    });

    it('should handle long note input with scroll management', async () => {
      await flowUtils.initializeTest(
        'Long Note Test',
        TEST_USERS.ASKER,
        TEST_LOCATIONS.STANDARD
      );
      await E2EHelpers.onHaveLogin(TEST_USERS.ASKER.phone, '123456');
      
      await flowUtils.completeAddressSelection();
      await flowUtils.completeServiceConfiguration();
      
      const longNote = 'This is a very long note that tests the input field capacity and scroll management when dealing with extensive text input that might extend beyond the normal viewport boundaries and require special handling for proper visibility and interaction.';
      
      await flowUtils.completeDateTimeSelection(longNote);
      await flowUtils.completePaymentConfirmation();
      
      const results = flowUtils.getResults();
      expect(results.success).toBe(true);
      expect(results.flow.datetime.note).toBe(longNote);
    });
  });

  describe('📈 Performance Benchmarking', () => {
    it('should benchmark different home types performance', async () => {
      const homeTypes = ['HOTEL', 'VILLA', 'APARTMENT'];
      const benchmarkResults = [];
      
      for (const homeType of homeTypes) {
        await flowUtils.initializeTest(
          `${homeType} Benchmark`,
          TEST_USERS.PERFORMANCE_ASKER,
          TEST_LOCATIONS.PERFORMANCE
        );
        await E2EHelpers.onHaveLogin(TEST_USERS.PERFORMANCE_ASKER.phone, '123456');
        
        const results = await flowUtils.executeRapidFlow({
          homeType,
          roomType: 'SINGLE',
          quantity: 1,
        });
        
        benchmarkResults.push({
          homeType,
          time: results.performance.totalTime,
          success: results.success,
        });
      }
      
      // Validate all home types complete successfully
      benchmarkResults.forEach(result => {
        expect(result.success).toBe(true);
        expect(result.time).toBeLessThan(PERFORMANCE_BENCHMARKS.FLOWS.complete);
      });
      
      console.log('🏠 Home Type Performance Benchmark:');
      benchmarkResults.forEach(result => {
        console.log(`   ${result.homeType}: ${result.time}ms`);
      });
    });
  });
});
