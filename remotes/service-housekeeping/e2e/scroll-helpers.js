/**
 * Enhanced Scroll Management Utilities for Housekeeping E2E Tests
 * 
 * Provides optimized scroll-to-reveal patterns with incremental scrolling,
 * element visibility validation, and viewport management for reliable E2E testing.
 */

const { element, by, waitFor } = require('detox');

/**
 * Incremental scroll with element visibility validation
 * Uses 150px increments to prevent overshooting and ensure reliable element access
 * 
 * @param {string} containerId - TestID of the scroll container
 * @param {string} targetElementId - TestID of the target element to reveal
 * @param {string} direction - Scroll direction ('up' or 'down')
 * @param {number} maxAttempts - Maximum scroll attempts before timeout
 * @param {number} scrollIncrement - Pixels to scroll per attempt (default: 150)
 */
const scrollToRevealElement = async (
  containerId,
  targetElementId,
  direction = 'down',
  maxAttempts = 10,
  scrollIncrement = 150
) => {
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    try {
      // Check if target element is visible
      await waitFor(element(by.id(targetElementId)))
        .toBeVisible()
        .withTimeout(1000);
      
      // Element is visible, return success
      return true;
    } catch (error) {
      // Element not visible, scroll incrementally
      await element(by.id(containerId)).scroll(scrollIncrement, direction);
      attempts++;
      
      // Small delay to allow UI to settle
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  }
  
  throw new Error(`Failed to reveal element ${targetElementId} after ${maxAttempts} scroll attempts`);
};

/**
 * Progressive scroll pattern for complex forms
 * Scrolls through form sections progressively, ensuring each section is accessible
 * 
 * @param {string} containerId - TestID of the scroll container
 * @param {Array<string>} sectionIds - Array of section testIDs to scroll through
 * @param {number} scrollIncrement - Pixels to scroll between sections
 */
const progressiveFormScroll = async (containerId, sectionIds, scrollIncrement = 200) => {
  for (const sectionId of sectionIds) {
    await scrollToRevealElement(containerId, sectionId, 'down', 8, scrollIncrement);
    
    // Verify section is accessible
    await waitFor(element(by.id(sectionId)))
      .toBeVisible()
      .withTimeout(2000);
  }
};

/**
 * Optimized scroll for booking flow steps
 * Handles the specific scroll patterns needed for housekeeping booking flow
 * 
 * @param {string} step - Booking step ('address', 'service', 'datetime', 'confirmation')
 * @param {string} containerId - TestID of the scroll container
 * @param {string} targetElementId - TestID of the target element
 */
const bookingFlowScroll = async (step, containerId, targetElementId) => {
  const scrollConfigs = {
    address: { increment: 100, maxAttempts: 5 },
    service: { increment: 150, maxAttempts: 8 },
    datetime: { increment: 150, maxAttempts: 6 },
    confirmation: { increment: 200, maxAttempts: 10 }
  };
  
  const config = scrollConfigs[step] || { increment: 150, maxAttempts: 8 };
  
  await scrollToRevealElement(
    containerId,
    targetElementId,
    'down',
    config.maxAttempts,
    config.increment
  );
};

/**
 * Bidirectional scroll with position memory
 * Scrolls to reveal element and can return to original position
 * 
 * @param {string} containerId - TestID of the scroll container
 * @param {string} targetElementId - TestID of the target element
 * @param {boolean} returnToTop - Whether to scroll back to top after interaction
 */
const scrollWithPositionMemory = async (containerId, targetElementId, returnToTop = false) => {
  let scrollDistance = 0;
  const scrollIncrement = 150;
  const maxAttempts = 10;
  
  for (let attempts = 0; attempts < maxAttempts; attempts++) {
    try {
      await waitFor(element(by.id(targetElementId)))
        .toBeVisible()
        .withTimeout(1000);
      
      // Element found, break the loop
      break;
    } catch (error) {
      // Scroll down incrementally
      await element(by.id(containerId)).scroll(scrollIncrement, 'down');
      scrollDistance += scrollIncrement;
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  }
  
  // Return to original position if requested
  if (returnToTop && scrollDistance > 0) {
    await element(by.id(containerId)).scroll(scrollDistance, 'up');
  }
};

/**
 * Smart scroll for payment confirmation screen
 * Handles the complex layout of the confirmation screen with multiple sections
 * 
 * @param {string} containerId - TestID of the scroll container (usually 'scrollViewStep4')
 * @param {string} targetSection - Target section ('location', 'task-detail', 'payment', 'submit')
 */
const confirmationScreenScroll = async (containerId, targetSection) => {
  const sectionScrollMap = {
    'location': { distance: 0, direction: 'down' },
    'task-detail': { distance: 200, direction: 'up' },
    'payment': { distance: 400, direction: 'up' },
    'submit': { distance: 600, direction: 'up' }
  };
  
  const scrollConfig = sectionScrollMap[targetSection];
  if (!scrollConfig) {
    throw new Error(`Unknown confirmation screen section: ${targetSection}`);
  }
  
  // Scroll to section
  if (scrollConfig.distance > 0) {
    await element(by.id(containerId)).scroll(
      scrollConfig.distance,
      scrollConfig.direction
    );
  }
  
  // Wait for UI to settle
  await new Promise(resolve => setTimeout(resolve, 300));
};

/**
 * Viewport boundary detection and adjustment
 * Detects if element is at viewport boundary and adjusts scroll accordingly
 * 
 * @param {string} containerId - TestID of the scroll container
 * @param {string} elementId - TestID of the element to check
 */
const adjustForViewportBoundary = async (containerId, elementId) => {
  try {
    // Try to interact with element
    await element(by.id(elementId)).tap();
  } catch (error) {
    // Element might be at boundary, try small adjustment scroll
    await element(by.id(containerId)).scroll(75, 'down');
    await new Promise(resolve => setTimeout(resolve, 200));
    
    // Retry interaction
    await element(by.id(elementId)).tap();
  }
};

/**
 * Performance-optimized scroll for rapid testing
 * Uses larger increments and shorter timeouts for faster test execution
 * 
 * @param {string} containerId - TestID of the scroll container
 * @param {string} targetElementId - TestID of the target element
 */
const rapidScroll = async (containerId, targetElementId) => {
  const rapidScrollIncrement = 300;
  const maxAttempts = 5;
  
  for (let attempts = 0; attempts < maxAttempts; attempts++) {
    try {
      await waitFor(element(by.id(targetElementId)))
        .toBeVisible()
        .withTimeout(500);
      return;
    } catch (error) {
      await element(by.id(containerId)).scroll(rapidScrollIncrement, 'down');
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  throw new Error(`Rapid scroll failed to reveal ${targetElementId}`);
};

/**
 * Scroll pattern for room type selection with quantity controls
 * Specialized scroll for the room type section with number spinners
 * 
 * @param {string} roomTypeContainer - TestID of room type container
 * @param {string} roomTypeName - Name of the room type (e.g., 'SINGLE', 'DOUBLE')
 */
const roomTypeScroll = async (roomTypeContainer, roomTypeName) => {
  // Scroll to reveal room type section
  await scrollToRevealElement(roomTypeContainer, `btnIncrement_${roomTypeName}`, 'down', 6, 150);
  
  // Ensure increment button is accessible
  await waitFor(element(by.id(`btnIncrement_${roomTypeName}`)))
    .toBeVisible()
    .withTimeout(2000);
};

/**
 * Multi-step scroll validation
 * Validates that all required elements in a sequence are accessible via scrolling
 * 
 * @param {string} containerId - TestID of the scroll container
 * @param {Array<string>} elementSequence - Array of element testIDs to validate
 */
const validateScrollSequence = async (containerId, elementSequence) => {
  const results = [];
  
  for (const elementId of elementSequence) {
    try {
      await scrollToRevealElement(containerId, elementId, 'down', 5, 150);
      results.push({ elementId, accessible: true });
    } catch (error) {
      results.push({ elementId, accessible: false, error: error.message });
    }
  }
  
  return results;
};

module.exports = {
  scrollToRevealElement,
  progressiveFormScroll,
  bookingFlowScroll,
  scrollWithPositionMemory,
  confirmationScreenScroll,
  adjustForViewportBoundary,
  rapidScroll,
  roomTypeScroll,
  validateScrollSequence,
};
