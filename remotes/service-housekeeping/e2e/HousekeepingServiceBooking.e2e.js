/**
 * Comprehensive E2E Test Suite for Housekeeping Service Booking
 *
 * Tests the complete sequential booking flow:
 * Address Selection → Service Configuration → Date/Time Selection → Payment/Confirmation
 *
 * Requirements:
 * - Uses mandatory testID-based element selection (no text selectors)
 * - Follows strict sequential flow dependency
 * - Implements scroll-to-reveal patterns for viewport management
 * - Optimized for 3-5 minute execution times
 * - Comprehensive flow validation with state persistence
 */

const {
  initData,
  tapId,
  tapText,
  waitForElement,
  expectElementVisible,
  expectIdToHaveText,
  scroll,
  typeToTextField,
  clearTextInput,
  removeIntroHouseKeeping,
  tapIdService,
} = require('./step-definition');

const { E2EHelpers } = require('../../../e2e/e2e.helpers');

// Test user data
const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Test Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const TASKER = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Test Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};

// Test location data
const TEST_LOCATION = {
  country: 'VN',
  isoCode: 'VN',
  phoneNumber: '0834567890',
  address: 'Công ty TNHH bTaskee, Hẻm 284/25 Lý Thường Kiệt, phường 14, Quận 10, Hồ Chí Minh, Việt Nam',
  district: 'Quận 10',
  homeType: 'HOME',
  locationName: 'Test Location',
  contact: 'Test Asker',
  countryCode: '+84',
  lat: 10.7331278,
  lng: 106.706233,
  shortAddress: '284/25 Lý Thường Kiệt',
  _id: 'test_location_id_001',
  city: 'Hồ Chí Minh',
  description: 'Test housekeeping location',
};

describe('Housekeeping Service Booking - Sequential Flow E2E Tests', () => {
  beforeEach(async () => {
    // Reset data and initialize test environment
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER]);

    // Setup user with housekeeping locations
    await initData('user/updateUser', {
      phone: ASKER.phone,
      isoCode: ASKER.isoCode,
      dataUpdate: {
        housekeepingLocations: [TEST_LOCATION],
      },
    });

    // Login user
    await E2EHelpers.onHaveLogin(ASKER.phone, '123456');
  });

  describe('Address Selection Flow', () => {
    it('should complete address selection step successfully', async () => {
      // Navigate to housekeeping service
      await tapIdService('postTaskServiceHOUSE_KEEPING');
      await removeIntroHouseKeeping();

      // Verify address selection screen is displayed
      await expectElementVisible('scrollChooseAddress');

      // Select existing location
      await tapId('address1');

      // Verify navigation to service configuration
      await expectElementVisible('home-type-selection-container');
    });

    it('should handle add new location flow', async () => {
      await tapIdService('postTaskServiceHOUSE_KEEPING');
      await removeIntroHouseKeeping();

      // Tap add new location button
      await tapId('btnAddNewLocation');

      // Verify navigation to address selection
      await expectElementVisible('scrollChooseAddress');
    });
  });

  describe('Service Configuration Flow', () => {
    beforeEach(async () => {
      // Complete address selection prerequisite
      await tapIdService('postTaskServiceHOUSE_KEEPING');
      await removeIntroHouseKeeping();
      await tapId('address1');
    });

    it('should complete service configuration step successfully', async () => {
      // Verify service configuration screen
      await expectElementVisible('home-type-selection-container');
      await expectElementVisible('room-type-selection-container');

      // Select home type (Hotel)
      await tapId('btnHostelType_HOTEL');

      // Configure room selection with scroll-to-reveal
      await scroll('room-type-selection-container', 200, 'down');
      await tapId('btnIncrement_SINGLE');

      // Verify price is calculated
      await expectElementVisible('btnNextStep3');

      // Proceed to next step
      await tapId('btnNextStep3');

      // Verify navigation to date/time selection
      await expectElementVisible('date-picker-component');
    });

    it('should handle room number input', async () => {
      await tapId('btnHostelType_HOTEL');
      await tapId('btnIncrement_SINGLE');

      // Scroll to room number input
      await scroll('room-type-selection-container', 300, 'down');

      // Input room number
      await typeToTextField('room-number-input', '101');

      // Verify input is accepted
      await expectElementVisible('btnNextStep3');
    });

    it('should handle working process navigation', async () => {
      await tapId('btnHostelType_HOTEL');
      await tapId('btnIncrement_SINGLE');

      // Scroll to working process button
      await scroll('room-type-selection-container', 400, 'down');
      await tapId('working-process-button');

      // Verify working process screen
      await expectElementVisible('working-process-container');
    });
  });

  describe('Date/Time Selection Flow', () => {
    beforeEach(async () => {
      // Complete previous steps
      await tapIdService('postTaskServiceHOUSE_KEEPING');
      await removeIntroHouseKeeping();
      await tapId('address1');
      await tapId('btnHostelType_HOTEL');
      await tapId('btnIncrement_SINGLE');
      await tapId('btnNextStep3');
    });

    it('should complete date/time selection step successfully', async () => {
      // Verify date/time selection screen
      await expectElementVisible('date-picker-component');
      await expectElementVisible('time-picker-component');

      // Interact with date picker (scroll to reveal if needed)
      await scroll('date-picker-component', 150, 'down');

      // Interact with time picker
      await scroll('time-picker-component', 150, 'down');

      // Add note for tasker
      await scroll('note-input-component', 200, 'down');
      await typeToTextField('note-input-component', 'Please be careful with fragile items');

      // Proceed to confirmation
      await expectElementVisible('btnNextStep3');
      await tapId('btnNextStep3');

      // Verify navigation to payment screen
      await expectElementVisible('scrollViewStep4');
    });

    it('should handle note input with scroll management', async () => {
      // Scroll to note section
      await scroll('note-input-component', 300, 'down');

      // Clear and input note
      await clearTextInput('note-input-component');
      await typeToTextField('note-input-component', 'Test note for E2E validation');

      // Verify note is accepted
      await expectElementVisible('btnNextStep3');
    });
  });

  describe('Payment and Confirmation Flow', () => {
    beforeEach(async () => {
      // Complete all previous steps
      await tapIdService('postTaskServiceHOUSE_KEEPING');
      await removeIntroHouseKeeping();
      await tapId('address1');
      await tapId('btnHostelType_HOTEL');
      await tapId('btnIncrement_SINGLE');
      await tapId('btnNextStep3');
      await tapId('btnNextStep3');
    });

    it('should complete full booking flow successfully', async () => {
      // Verify confirmation screen components
      await expectElementVisible('scrollViewStep4');
      await expectElementVisible('location-post-task-container');
      await expectElementVisible('task-detail-container');
      await expectElementVisible('payment-detail-container');
      await expectElementVisible('payment-method-container');

      // Scroll through confirmation details
      await scroll('scrollViewStep4', 200, 'up');

      // Verify task details are displayed
      await expectElementVisible('txtTaskDetail');

      // Scroll to payment section
      await scroll('scrollViewStep4', 300, 'up');

      // Submit booking
      await expectElementVisible('btnSubmitPostTask');
      await tapId('btnSubmitPostTask');

      // Verify successful booking
      await waitForElement('Đăng việc thành công', 5000, 'text');
    });

    it('should handle payment method selection with scroll', async () => {
      // Scroll to payment method section
      await scroll('scrollViewStep4', 400, 'up');

      // Verify payment methods are accessible
      await expectElementVisible('payment-method-container');

      // Test scroll back to top
      await scroll('scrollViewStep4', 400, 'down');
      await expectElementVisible('location-post-task-container');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle incomplete service configuration', async () => {
      await tapIdService('postTaskServiceHOUSE_KEEPING');
      await removeIntroHouseKeeping();
      await tapId('address1');

      // Try to proceed without selecting home type
      await expectElementVisible('btnNextStep3');
      // Button should be disabled or show validation
    });

    it('should handle network errors gracefully', async () => {
      // This would require network simulation
      // Implementation depends on test environment setup
    });
  });

  describe('Viewport Management and Scrolling', () => {
    it('should handle scroll-to-reveal patterns effectively', async () => {
      await tapIdService('postTaskServiceHOUSE_KEEPING');
      await removeIntroHouseKeeping();
      await tapId('address1');
      await tapId('btnHostelType_HOTEL');

      // Test incremental scrolling patterns (150px increments for reliability)
      await scroll('room-type-selection-container', 150, 'down');
      await scroll('room-type-selection-container', 150, 'down');
      await scroll('room-type-selection-container', 150, 'down');

      // Verify elements become accessible
      await expectElementVisible('btnIncrement_SINGLE');

      // Test scroll back
      await scroll('room-type-selection-container', 300, 'up');
    });

    it('should manage viewport during complete flow', async () => {
      // Test viewport management across entire booking flow
      await tapIdService('postTaskServiceHOUSE_KEEPING');
      await removeIntroHouseKeeping();

      // Address selection with scroll
      await scroll('scrollChooseAddress', 100, 'down');
      await tapId('address1');

      // Service configuration with progressive scroll
      await scroll('home-type-selection-container', 100, 'down');
      await tapId('btnHostelType_HOTEL');
      await scroll('room-type-selection-container', 200, 'down');
      await tapId('btnIncrement_SINGLE');
      await tapId('btnNextStep3');

      // Date/time with scroll management
      await scroll('date-picker-component', 150, 'down');
      await scroll('time-picker-component', 150, 'down');
      await tapId('btnNextStep3');

      // Confirmation with comprehensive scroll
      await scroll('scrollViewStep4', 200, 'up');
      await scroll('scrollViewStep4', 200, 'up');
      await tapId('btnSubmitPostTask');

      await waitForElement('Đăng việc thành công', 5000, 'text');
    });
  });

  describe('Performance and Optimization', () => {
    it('should complete full booking flow within 3-5 minutes', async () => {
      const startTime = Date.now();

      // Execute complete flow with optimized interactions
      await tapIdService('postTaskServiceHOUSE_KEEPING');
      await removeIntroHouseKeeping();
      await tapId('address1');
      await tapId('btnHostelType_HOTEL');
      await tapId('btnIncrement_SINGLE');
      await tapId('btnNextStep3');
      await tapId('btnNextStep3');
      await tapId('btnSubmitPostTask');

      await waitForElement('Đăng việc thành công', 5000, 'text');

      const executionTime = Date.now() - startTime;
      const maxExecutionTime = 5 * 60 * 1000; // 5 minutes in milliseconds

      expect(executionTime).toBeLessThan(maxExecutionTime);
    });

    it('should handle rapid sequential interactions', async () => {
      // Test rapid interaction patterns for performance
      await tapIdService('postTaskServiceHOUSE_KEEPING');
      await removeIntroHouseKeeping();
      await tapId('address1');

      // Rapid service configuration
      await tapId('btnHostelType_HOTEL');
      await tapId('btnIncrement_SINGLE');
      await tapId('btnIncrement_SINGLE'); // Add second room
      await tapId('btnNextStep3');

      // Quick date/time selection
      await tapId('btnNextStep3');

      // Fast confirmation
      await tapId('btnSubmitPostTask');

      await waitForElement('Đăng việc thành công', 3000, 'text');
    });
  });

  describe('State Persistence Validation', () => {
    it('should maintain selections across navigation steps', async () => {
      await tapIdService('postTaskServiceHOUSE_KEEPING');
      await removeIntroHouseKeeping();
      await tapId('address1');

      // Configure service
      await tapId('btnHostelType_HOTEL');
      await tapId('btnIncrement_SINGLE');
      await typeToTextField('room-number-input', '205');
      await tapId('btnNextStep3');

      // Add note
      await typeToTextField('note-input-component', 'Persistent state test');
      await tapId('btnNextStep3');

      // Verify state persistence in confirmation
      await expectElementVisible('txtTaskDetail');
      await expectElementVisible('task-detail-container');

      // Complete booking to verify all data persisted
      await tapId('btnSubmitPostTask');
      await waitForElement('Đăng việc thành công', 5000, 'text');
    });
  });
});
