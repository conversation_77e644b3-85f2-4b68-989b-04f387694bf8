import React, { useMemo, useRef, useState } from 'react';
import { ScrollView, TouchableOpacity } from 'react-native';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  FastImage,
  FontSizes,
  getTextWithLocale,
  IconAssets,
  IconImage,
  IHouseKeepingOption,
  IImage,
  IListRoomImage,
  IRoomType,
  Spacing,
  ToastHelpers,
  useAppLoadingStore,
  useUploadImage,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useI18n, usePostTask } from '@hooks';
import { usePostTaskStore } from '@stores';

import { NumberSpinner } from '../NumberSpinner';
import { UploadImageModal, UploadImageModalHandle } from '../UploadImageModal';
import { styles } from './styles';

const MAX_IMAGES = 10;

const ImageEmpty = ({ onPress }: { onPress?: () => void }) => {
  const { t } = useI18n();

  return (
    <BlockView style={styles.emptyImageContainer}>
      <TouchableOpacity onPress={onPress}>
        <BlockView
          row
          center
          style={styles.emptyImageButton}
        >
          <IconImage
            source={IconAssets.icCamera}
            size={20}
            color={ColorsV2.orange500}
          />
          <CText
            color={ColorsV2.orange500}
            margin={{ left: Spacing.SPACE_04 }}
          >
            {t('TAKE_PICTURE')}
          </CText>
        </BlockView>
      </TouchableOpacity>
    </BlockView>
  );
};

interface ITakeRoomPictureProps {
  options?: IHouseKeepingOption[];
}

interface ITakePictureByRoomProps {
  title: string;
  name: string;
  listImages?: string[];
  onChangeImages?: (value: IListRoomImage) => void;
}

const TakePictureByRoom = ({
  title,
  name,
  listImages,
  onChangeImages,
}: ITakePictureByRoomProps) => {
  const uploadImageModalRef = useRef<UploadImageModalHandle>(null);
  const { showLoading, hideLoading } = useAppLoadingStore();
  const { t } = useI18n();
  const [currentImages, setCurrentImages] = useState<string[]>(
    listImages || [],
  );
  const { getMultipleLinkImages } = useUploadImage();

  const lengthCurrentImages = currentImages?.length || 0;

  const showToastMaxImages = () => {
    ToastHelpers.showWarning({
      message: t('PLS_SELECT_MAX_IMAGES', { max: MAX_IMAGES }),
    });
  };

  const _onUploadImage = () => {
    if (lengthCurrentImages < MAX_IMAGES) {
      uploadImageModalRef?.current?.open?.();
    } else {
      showToastMaxImages();
    }
  };

  const _onUploadImageSuccess = async (imageUrls?: string[]) => {
    const newImages = [...(imageUrls || []), ...(currentImages || [])];
    if (newImages.length <= MAX_IMAGES) {
      showLoading();
      try {
        const imagesUpload = await getMultipleLinkImages({
          images: newImages.map((uri) => ({
            uri,
            name: uri.split('/').pop() || 'image',
            type: 'image/png',
          })) as IImage[],
          endPath: 'housekeeping/room-images',
        });
        const listLinkImages = imagesUpload
          .map((image) => image?.link)
          .filter(Boolean) as string[];
        setCurrentImages(listLinkImages);
        onChangeImages?.({
          name,
          images: listLinkImages,
        });
      } catch (error) {
        console.error('Upload failed:', error);
      } finally {
        hideLoading();
      }
    } else {
      showToastMaxImages();
    }
  };

  const onRemoveImage = (index: number) => () => {
    const newImages = [...(currentImages || [])];
    newImages.splice(index, 1);
    setCurrentImages(newImages);
    onChangeImages?.({
      name,
      images: newImages,
    });
  };

  return (
    <BlockView style={styles.roomImageContainer}>
      <CText
        size={FontSizes.SIZE_14}
        margin={{ bottom: Spacing.SPACE_08 }}
      >
        {title}
      </CText>
      <ConditionView
        condition={isEmpty(currentImages)}
        viewTrue={<ImageEmpty onPress={_onUploadImage} />}
        viewFalse={
          <BlockView>
            <BlockView row>
              <BlockView style={styles.imageScrollContainer}>
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                >
                  {currentImages?.map((imageUri, index) => (
                    <BlockView
                      key={index}
                      style={styles.imageItemContainer}
                    >
                      <FastImage
                        source={{ uri: imageUri }}
                        style={styles.roomImage}
                      />
                      <TouchableOpacity
                        style={styles.removeButton}
                        onPress={onRemoveImage(index)}
                      >
                        <IconImage
                          source={IconAssets.icRemoveCircle}
                          size={20}
                        />
                      </TouchableOpacity>
                    </BlockView>
                  ))}
                </ScrollView>
              </BlockView>
              <BlockView
                center
                padding={{ left: Spacing.SPACE_16 }}
              >
                <TouchableOpacity onPress={_onUploadImage}>
                  <BlockView style={styles.addImageButton}>
                    <IconImage
                      source={IconAssets.icPlusFill}
                      size={24}
                      color={ColorsV2.orange500}
                    />
                  </BlockView>
                </TouchableOpacity>
              </BlockView>
            </BlockView>
          </BlockView>
        }
      />
      <UploadImageModal
        ref={uploadImageModalRef}
        maxFiles={MAX_IMAGES - lengthCurrentImages}
        onUploadImageSuccess={_onUploadImageSuccess}
      />
    </BlockView>
  );
};

export const TakeRoomPicture = ({ options }: ITakeRoomPictureProps) => {
  const { t } = useI18n();
  const {
    rooms,
    options: selectedOptions,
    setOptions,
    listRoomsImages,
    updateRoomImages,
  } = usePostTaskStore();
  const { getPrice } = usePostTask();

  const totalQuantity = useMemo(() => {
    return rooms.reduce(
      (sum: number, room: IRoomType) => sum + (room?.quantity || 0),
      0,
    );
  }, [rooms]);

  const optionsRoomNeedToSetUp = options?.find(
    (item: IHouseKeepingOption) => item?.name === 'SETUP_ROOM',
  );

  const currentSetupRoom = selectedOptions?.find(
    (item: IHouseKeepingOption) => item?.name === 'SETUP_ROOM',
  );

  const handleSetupRoomChange = async (value: number) => {
    const updatedOptions = [...selectedOptions];
    const existingIndex = updatedOptions.findIndex(
      (item) => item.name === 'SETUP_ROOM',
    );

    if (value === 0) {
      // Remove if quantity is 0
      if (existingIndex >= 0) {
        updatedOptions.splice(existingIndex, 1);
      }
    } else if (optionsRoomNeedToSetUp) {
      const setupRoomOption = {
        name: optionsRoomNeedToSetUp.name,
        text: optionsRoomNeedToSetUp.text,
        quantity: value,
      };

      if (existingIndex >= 0) {
        // Update existing
        updatedOptions[existingIndex] = setupRoomOption;
      } else {
        // Add new
        updatedOptions.push(setupRoomOption);
      }
    }

    await setOptions(updatedOptions);
    getPrice();
  };

  return (
    <BlockView style={styles.container}>
      <BlockView>
        {rooms?.map((item: IRoomType) => {
          const currentRoomImages = listRoomsImages?.find(
            (room: IListRoomImage) => room.name === item.name,
          );
          return (
            <TakePictureByRoom
              key={item?.name}
              title={getTextWithLocale(item.text)}
              name={item?.name || ''}
              listImages={currentRoomImages?.images || []}
              onChangeImages={(value: IListRoomImage) =>
                updateRoomImages(value)
              }
            />
          );
        })}
      </BlockView>

      <ConditionView
        condition={!isEmpty(optionsRoomNeedToSetUp)}
        viewTrue={
          <BlockView
            row
            jBetween
          >
            <CText
              bold
              style={styles.left}
            >
              {t('NUMBER_ROOM_NEED_TO_SET_UP')}
            </CText>
            <BlockView style={styles.right}>
              <NumberSpinner
                amount={currentSetupRoom?.quantity || 0}
                onPress={handleSetupRoomChange}
                maxValue={totalQuantity}
                size={16}
              />
            </BlockView>
          </BlockView>
        }
      />
    </BlockView>
  );
};
