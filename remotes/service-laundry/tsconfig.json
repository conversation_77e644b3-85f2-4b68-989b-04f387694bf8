{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"baseUrl": "./", "paths": {"@navigation/*": ["src/navigation/*"], "@app": ["src/App.tsx"], "@hooks": ["src/hooks"], "@screens": ["src/screens"], "@images": ["src/assets/images"], "@components": ["src/components"], "@i18n": ["src/i18n"], "@types": ["src/types"], "@stores": ["src/stores"], "@apis": ["src/apis"], "@constants": ["src/constants"]}}}