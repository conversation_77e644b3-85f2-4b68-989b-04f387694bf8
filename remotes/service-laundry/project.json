{"name": "service-laundry", "targets": {"start": {"executor": "nx:run-commands", "options": {"command": "yarn start", "cwd": "remotes/service-laundry"}}, "install": {"executor": "nx:run-commands", "options": {"command": "yarn install", "cwd": "remotes/service-laundry"}}, "build": {"executor": "nx:run-commands", "options": {"command": "yarn build", "cwd": "remotes/service-laundry"}}, "deploy": {"executor": "nx:run-commands", "options": {"command": "yarn deploy", "cwd": "remotes/service-laundry"}}, "reset": {"executor": "nx:run-commands", "options": {"command": "yarn reset", "cwd": "remotes/service-laundry"}}}, "tags": ["scope:service-laundry"]}