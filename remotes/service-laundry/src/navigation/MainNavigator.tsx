import '../i18n';

import React, { useEffect } from 'react';
import {
  Colors,
  FontFamily,
  Icon,
  SERVICES,
  TouchableOpacity,
  useSettingsStore,
} from '@btaskee/design-system';
import {
  createNativeStackNavigator,
  NativeStackNavigationOptions,
} from '@react-navigation/native-stack';

import { useI18n } from '@hooks';
import {
  ChooseAddress,
  ChooseDateTime,
  ChooseService,
  ConfirmAndPayment,
  IntroService,
  PostTaskSuccess,
} from '@screens';
import { usePostTaskStore } from '@stores';

import { RouteName } from './RouteName';
import { MainStackParamList } from './type';

const Main = createNativeStackNavigator<MainStackParamList>();

const MainNavigator = () => {
  const { t } = useI18n();
  const { setService } = usePostTaskStore();
  const { settings } = useSettingsStore();

  useEffect(() => {
    initData();
  }, []);

  // TODO: init data for cleaning service
  const initData = async () => {
    const laundryService = settings?.services?.find(
      (service) => service?.name === SERVICES.LAUNDRY,
    );

    setService(laundryService);
  };
  const renderHeaderLeft = (navigation: any) => {
    return (
      <TouchableOpacity
        onPress={() => navigation?.goBack()}
        activeOpacity={0.7}>
        <Icon
          name="icBack"
          size={24}
          color={Colors.BLACK}
        />
      </TouchableOpacity>
    );
  };

  return (
    <Main.Navigator
      screenOptions={({ navigation }): NativeStackNavigationOptions => ({
        headerShown: true,
        headerLeft: () => renderHeaderLeft(navigation),
        animation: 'slide_from_right',
        animationDuration: 200,
        contentStyle: { backgroundColor: Colors.WHITE },
        headerStyle: {
          backgroundColor: Colors.WHITE,
        },
        headerTitleStyle: {
          color: Colors.BLACK,
          fontSize: 18,
          fontFamily: FontFamily.bold,
        },
      })}>
      <Main.Screen
        name={RouteName.ChooseAddress}
        component={ChooseAddress as any}
        options={{ title: t('LIST_OF_LOCATIONS') }}
      />
      <Main.Screen
        name={RouteName.ChooseService}
        component={ChooseService}
      />
      <Main.Screen
        name={RouteName.ChooseDateTime}
        component={ChooseDateTime}
        options={{ title: t('WORK_TIME_TITLE') }}
      />
      <Main.Screen
        name={RouteName.ConfirmAndPayment}
        component={ConfirmAndPayment}
        options={{ title: t('PT2_CONFIRM_HEADER_TITLE') }}
      />
      <Main.Screen
        name={RouteName.PostTaskSuccess}
        component={PostTaskSuccess}
        options={{ headerShown: false }}
      />
    </Main.Navigator>
  );
};

export default MainNavigator;
