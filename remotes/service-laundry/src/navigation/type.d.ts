import { IAddress } from '@btaskee/design-system';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { RouteName } from './RouteName';

type MainStackParamList = {
  [RouteName.ChooseAddress]: undefined;
  [RouteName.ChooseService]: undefined;
  [RouteName.ChooseDateTime]: undefined;
  [RouteName.ConfirmAndPayment]: undefined;
  [RouteName.PostTaskSuccess]: undefined;
  [RouteName.EditLocation]: { location: IAddress };
  [RouteName.WorkingProcess]: undefined;
  [RouteName.IntroService]:
    | {
        isHideButton?: boolean;
      }
    | undefined;
  [RouteName.ChooseWorkTime]: undefined;
};

export type ParamsNavigationList = MainStackParamList;

export type NavigationProps<T extends keyof MainStackParamList> =
  NativeStackNavigationProp<MainStackParamList, T>;
