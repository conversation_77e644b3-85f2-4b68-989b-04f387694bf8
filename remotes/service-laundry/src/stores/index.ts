import {
  IAddress,
  IDate,
  IPrice,
  IRelatedTask,
  IService,
} from '@btaskee/design-system';
import { create } from 'zustand';

import {
  LaundryCity,
  LaundryDryCleanData,
  LaundryOtherData,
  LaundryWashingData,
  PaymentMethodData,
  PromotionData,
} from '../types/task';

interface LaundryState {
  // Common post-task state
  address: IAddress;
  duration: number;
  date: IDate | null;
  note: string;
  isApplyNoteForAllTask: boolean;
  isFavouriteTasker: boolean;
  price: IPrice | null;
  service: IService | null;
  paymentMethod: PaymentMethodData;
  promotion: PromotionData | null;
  relatedTask: IRelatedTask | null;

  // Laundry-specific state
  washing: LaundryWashingData | null;
  dryClean: LaundryDryCleanData | null;
  others: LaundryOtherData | null;
  otherText: string;
  city: LaundryCity[] | null;
  collectionDate: IDate | null;
  loadingPrice: boolean;

  // Actions
  setAddress: (address: IAddress) => void;
  setDuration: (duration: number) => void;
  setDateTime: (date: IDate) => void;
  setNote: (note: string) => void;
  setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) => void;
  setIsFavouriteTasker: (isFavouriteTasker: boolean) => void;
  setPrice: (price: IPrice | null) => void;
  setPaymentMethod: (paymentMethod: PaymentMethodData) => void;
  setPromotion: (promotion: PromotionData | null) => void;
  setRelatedTask: (relatedTask: IRelatedTask) => void;
  setService: (service: IService) => void;
  setLoadingPrice: (loadingPrice: boolean) => void;

  // Laundry-specific actions
  setWashing: (washing: LaundryWashingData | null) => void;
  setDryClean: (dryClean: LaundryDryCleanData | null) => void;
  setOthers: (others: LaundryOtherData | null) => void;
  setOtherText: (otherText: string) => void;
  setCity: (city: LaundryCity[] | null) => void;
  setCollectionDate: (collectionDate: IDate | null) => void;
  resetState: () => void;
  resetLaundryState: () => void;
}

export const usePostTaskStore = create<LaundryState>((set) => ({
  // Initial state
  address: {} as IAddress,
  duration: 0,
  date: null,
  note: '',
  isApplyNoteForAllTask: false,
  isFavouriteTasker: false,
  price: null,
  service: null,
  paymentMethod: {
    name: 'Cash',
    label: 'PAYMENT_METHOD_DIRECT_CASH',
    value: 'CASH',
    icon: '',
  },
  promotion: null,
  relatedTask: null,

  // Laundry-specific initial state
  washing: null,
  dryClean: null,
  others: null,
  otherText: '',
  city: null,
  collectionDate: null,
  loadingPrice: false,

  // Common actions
  setAddress: (address: IAddress) => set({ address }),
  setDuration: (duration: number) => set({ duration }),
  setDateTime: (date: IDate) => set({ date }),
  setNote: (note: string) => set({ note }),
  setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) =>
    set({ isApplyNoteForAllTask }),
  setIsFavouriteTasker: (isFavouriteTasker: boolean) =>
    set({ isFavouriteTasker }),
  setPrice: (price: IPrice | null) => set({ price }),
  setPaymentMethod: (paymentMethod: PaymentMethodData) =>
    set({ paymentMethod }),
  setPromotion: (promotion: PromotionData | null) => set({ promotion }),
  setRelatedTask: (relatedTask: IRelatedTask) => set({ relatedTask }),
  setService: (service: IService) => set({ service }),
  setLoadingPrice: (loadingPrice: boolean) => set({ loadingPrice }),

  // Laundry-specific actions
  setWashing: (washing: LaundryWashingData | null) => set({ washing }),
  setDryClean: (dryClean: LaundryDryCleanData | null) => set({ dryClean }),
  setOthers: (others: LaundryOtherData | null) => set({ others }),
  setOtherText: (otherText: string) => set({ otherText }),
  setCity: (city: LaundryCity[] | null) => set({ city }),
  setCollectionDate: (collectionDate: IDate | null) => set({ collectionDate }),

  resetState: () =>
    set({
      address: {} as IAddress,
      duration: 0,
      date: null,
      note: '',
      isApplyNoteForAllTask: false,
      isFavouriteTasker: false,
      price: null,
      service: null,
      paymentMethod: {
        name: 'Cash',
        label: 'PAYMENT_METHOD_DIRECT_CASH',
        value: 'CASH',
        icon: '',
      },
      promotion: null,
      relatedTask: null,
      washing: null,
      dryClean: null,
      others: null,
      otherText: '',
      city: null,
      collectionDate: null,
    }),

  resetLaundryState: () =>
    set({
      washing: null,
      dryClean: null,
      others: null,
      otherText: '',
      city: null,
      collectionDate: null,
    }),
}));
