import { DateTimeHelpers, PAYMENT_METHOD } from '@btaskee/design-system';
import { IParamsGetPrice } from '@types';
import { isEmpty } from 'lodash-es';

import {
  LaundryCity,
  LaundryDryCleanData,
  LaundryOtherData,
  LaundryWashingData,
  TaskPaymentData,
} from '../types/task';

/**
 * Builds the detail laundry data structure from laundry components
 * Purpose: Combines different laundry service data (washing, dry cleaning, others) into a unified structure for task creation
 * @param laundryData - Object containing all laundry service components
 * @param laundryData.washing - Washing service data with items and quantities (optional)
 * @param laundryData.dryClean - Dry cleaning service data with items and quantities (optional)
 * @param laundryData.others - Other laundry services data (optional)
 * @param laundryData.otherText - Additional text description for custom services (optional)
 * @param laundryData.city - City-specific configuration for laundry services (optional)
 * @returns {object} Unified laundry detail object containing all selected services and configurations
 */
export const buildDetailLaundryData = (laundryData: {
  washing?: LaundryWashingData | null;
  dryClean?: LaundryDryCleanData | null;
  others?: LaundryOtherData | null;
  otherText?: string;
  city?: LaundryCity[] | null;
}): any => {
  const { washing, dryClean, others, otherText, city } = laundryData;
  const detail: any = {};

  // Add city if exists
  if (city) {
    detail.city = city;
  }

  // Add washing data if exists
  if (washing) {
    detail.washing = washing;
  }

  // Add dry clean data if exists
  if (dryClean) {
    detail.dryClean = dryClean;
  }

  // Add others data if exists
  if (others) {
    detail.others = others;
  }

  // Handle otherText data - based on unicorn logic
  if (otherText) {
    const otherTextData = {
      data: otherText,
    };

    if (others) {
      // If others already exists, add data to it
      detail.others = {
        ...others,
        data: otherText,
      };
    } else {
      // If others doesn't exist, create new with otherText
      detail.others = otherTextData;
    }
  }

  return detail;
};

/**
 * Builds payment data with validation for task posting
 * Purpose: Transforms payment method selection into standardized payment data structure required by the API
 * @param paymentMethod - Payment method object containing payment type and associated data
 * @param paymentMethod.value - Payment method type (card, virtualAccount, trueMoney, etc.)
 * @param paymentMethod.cardInfo - Credit card information object (for card payments)
 * @param paymentMethod.bank - Bank information (for virtual account payments)
 * @param paymentMethod.walletInfo - Wallet information (for trueMoney payments)
 * @returns {TaskPaymentData} Standardized payment data object with method and payment-specific details
 */
export const buildTaskPaymentData = (paymentMethod: any): TaskPaymentData => {
  if (!paymentMethod?.value) {
    return {
      method: '',
    };
  }

  const paymentData: TaskPaymentData = {
    method: paymentMethod.value,
  };

  switch (paymentMethod.value) {
    case PAYMENT_METHOD.card:
      paymentData.cardId = paymentMethod.cardInfo._id;
      break;

    case PAYMENT_METHOD.virtualAccount:
      paymentData.bank = paymentMethod.bank;
      break;

    case PAYMENT_METHOD.trueMoney:
      if (paymentMethod.walletInfo) {
        Object.assign(paymentData, paymentMethod.walletInfo);
      }
      break;

    default:
      break;
  }

  return paymentData;
};

/**
 * Builds pricing data with validation for laundry service price calculation
 * Purpose: Constructs comprehensive pricing request data combining address, schedule, and service details for API calls
 * @param address - Delivery address object containing location and contact information
 * @param date - Scheduled service date and time
 * @param duration - Estimated service duration in minutes
 * @param service - Service configuration object with ID and settings
 * @param paymentMethod - Selected payment method object
 * @param isoCode - Country/region ISO code for localization
 * @param laundryData - Complete laundry service data object with all selected services
 * @returns {IParamsGetPrice | null} Pricing request object for API call, or null if required data is missing
 */
export const buildPricingData = (
  address: any,
  date: Date,
  duration: number,
  service: any,
  paymentMethod: any,
  isoCode: string,
  laundryData: {
    washing?: LaundryWashingData | null;
    dryClean?: LaundryDryCleanData | null;
    others?: LaundryOtherData | null;
    otherText?: string;
    city?: LaundryCity[] | null;
  },
): IParamsGetPrice | null => {
  if (!address || !date) {
    return null;
  }

  const timezone = DateTimeHelpers.getTimezoneByCity(address.city);
  const detailLaundryData = buildDetailLaundryData(laundryData);

  // If no laundry data is selected, return null
  if (isEmpty(detailLaundryData)) {
    return null;
  }

  const task: IParamsGetPrice['task'] = {
    timezone,
    date: DateTimeHelpers.formatToString({ timezone, date }),
    autoChooseTasker: true,
    taskPlace: {
      country: address.country,
      city: address.city,
      district: address.district,
    },
    homeType: address.homeType,
    duration,
    detailLaundry: detailLaundryData,
  };

  if (paymentMethod?.value) {
    task.payment = {
      method: paymentMethod.value,
    };
  }

  return { task, service: { _id: service._id }, isoCode };
};

/**
 * Validates laundry data structure to ensure required services are selected
 * Purpose: Checks if at least one laundry service is selected before allowing price calculation or task posting
 * @param laundryData - Laundry service data object to validate
 * @param laundryData.washing - Washing service data (optional)
 * @param laundryData.dryClean - Dry cleaning service data (optional)
 * @param laundryData.others - Other services data (optional)
 * @param laundryData.otherText - Custom service text (optional)
 * @param laundryData.city - City configuration (optional)
 * @returns {boolean} True if at least one service is selected, false if no services are selected
 */
export const validateLaundryData = (laundryData: {
  washing?: LaundryWashingData | null;
  dryClean?: LaundryDryCleanData | null;
  others?: LaundryOtherData | null;
  otherText?: string;
  city?: LaundryCity[] | null;
}): boolean => {
  const detailLaundryData = buildDetailLaundryData(laundryData);
  return !isEmpty(detailLaundryData);
};
