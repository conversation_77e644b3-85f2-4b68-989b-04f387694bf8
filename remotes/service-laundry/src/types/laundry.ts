import type { IObjectText } from '@btaskee/design-system';

export interface ILaundryItem {
  text: IObjectText;
  quantity: number;
}

export interface ILaundryService {
  text: IObjectText;
  dataV2?: ILaundryItem[];
  data?: {
    text?: string;
  } & ILaundryItem[];
}

export interface ILaundryServiceProps {
  washing?: ILaundryService | null;
  dryClean?: ILaundryService | null;
  others?: ILaundryService | null;
}
