import {
  IDeviceInfo,
  ILocation,
  IService,
  IUser,
} from '@btaskee/design-system';

export interface IDataBooking {
  address?: string;
  locations?: ILocation[];
  user?: IUser;
  service?: IService;
  homeNumber?: string;
  homeType?: string;
  finalCost?: {
    amount: number;
  };
  // Additional properties
  contactName?: string;
  lat?: number;
  lng?: number;
  phone?: string;
  countryCode?: string;
  description?: string;
  askerId?: string;
  autoChooseTasker?: boolean;
  date?: string;
  timezone?: string;
  deviceInfo?: IDeviceInfo;
  duration?: number;
  houseNumber?: string;
  isoCode?: string;
  payment?: any;
  serviceId?: string;
  taskPlace: {
    city?: string;
    country?: string;
    district?: string;
    isAddressMaybeWrong?: boolean;
  };
  updateTaskNoteToUser?: boolean;
  shortAddress?: string;
  taskNote?: string;
  promotion?: any;
  weekday?: number[];
  detailCarpet?: any;
}

export type IParamsCheckTaskSameTime = {
  taskDate: string;
  serviceId: string;
};
