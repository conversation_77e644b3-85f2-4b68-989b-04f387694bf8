import { IObjectText } from '@btaskee/design-system';

export interface LaundryWashingData {
  type: string;
  text: IObjectText;
  dataV2?: LaundryWashingItem[];
  data?: {
    quantity: number;
    price: number;
  };
}

export interface LaundryWashingItem {
  type: string;
  quantity: number;
  price?: number;
  text?: IObjectText;
}

export interface LaundryDryCleanData {
  type: string;
  text: IObjectText;
  data: any[];
}

export interface LaundryOtherData {
  type: string;
  text: IObjectText;
  dataV2?: LaundryOtherItem[];
  data?: any[];
}

export interface LaundryOtherItem {
  type: string;
  quantity: number;
  text?: IObjectText;
}

export interface LaundryCity {
  name: string;
}

export interface LaundryDetail {
  type: string;
  text: any;
  dataV2?: any[];
}

export interface PaymentMethodData {
  name: string;
  label: string;
  value: string;
  icon: string;
  cardInfo?: {
    _id: string;
  };
  bank?: string;
  walletInfo?: Record<string, any>;
}

export interface PromotionData {
  _id: string;
  name: string;
  code: string;
  discount: number;
  type: string;
}

export interface CleaningData {
  [key: string]: any;
}

export interface TaskPaymentData {
  method: string;
  cardId?: string;
  bank?: string;
  [key: string]: any;
}

export interface TaskPlace {
  country: string;
  city: string;
  district: string;
  isAddressMaybeWrong?: boolean;
}

export interface TaskSource {
  from: string;
  taskId: string;
}

// Extended task data interface to include all required properties
export interface ExtendedTaskData {
  isTetBooking?: boolean;
  source?: TaskSource;
  detailLaundry?: any;
  taskNote?: string;
  isSendToFavTaskers?: boolean;
  updateTaskNoteToUser?: boolean;
  taskPlace?: TaskPlace;
}

// Store state interfaces for better typing
export interface PostTaskState {
  address: any;
  date: any;
  duration: number;
  service: any;
  paymentMethod: PaymentMethodData;
  isApplyNoteForAllTask: boolean;
  note: string;
  isFavouriteTasker: boolean;
  relatedTask: any;
  washing: LaundryWashingData | null;
  dryClean: LaundryDryCleanData | null;
  others: LaundryOtherData | null;
  otherText: string;
  city: LaundryCity[] | null;
  collectionDate: any;
}

export interface AppState {
  user: any;
  isoCode: string;
}
