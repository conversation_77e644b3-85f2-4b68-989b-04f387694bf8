import { DateTimeHelpers, IDate, IObjectText } from '@btaskee/design-system';
import { IDuration } from '@types';
import { ConfigType } from 'dayjs';
import { isEmpty } from 'lodash-es';

import { usePostTaskStore } from '@stores';

import { LaundryDryCleanData, LaundryOtherData } from '../types/task';
import { usePostTask } from './usePostTask';

interface CleaningDetailItem {
  type: string;
  text: IObjectText;
  price: number;
  unit?: IObjectText;
}

interface UpdateWashingParams {
  type?: string;
  data?: CleaningDetailItem;
  quantity?: number;
  laundryDetailText?: IObjectText;
  reset?: boolean;
}

interface UpdateDryCleanParams {
  type?: string;
  data?: any[];
  text?: IObjectText;
  reset?: boolean;
}

interface UpdateOtherParams {
  type?: string;
  data?: CleaningDetailItem;
  quantity?: number;
  laundryDetailText?: IObjectText;
  reset?: boolean;
}

interface UpdateOtherTextParams {
  type?: string;
  data?: any;
  text?: IObjectText;
  reset?: boolean;
}

export const useChangeData = () => {
  const {
    setDateTime,
    setDuration,
    setWashing: setWashingStore,
    setDryClean: setDryCleanStore,
    setOthers: setOthersStore,
    washing,
    others,
    address,
    setCollectionDate,
  } = usePostTaskStore();
  const { getPrice } = usePostTask();
  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

  const onChangeDuration = (newDuration: IDuration) => {
    setDuration(newDuration.duration);
    // get price
    getPrice();
  };

  const onChangeDateTime = (newDate: IDate) => {
    const currentState = usePostTaskStore.getState();
    const { date } = currentState;

    const isSame = DateTimeHelpers.checkIsSame({
      firstDate: date,
      secondDate: newDate,
      timezone,
    });
    // check spam, call api with same data
    if (isSame) return;
    // set new date time
    setDateTime(newDate);
    // get price again
    getPrice();
  };

  const updateWashing = async (params: UpdateWashingParams) => {
    const { type, data, quantity, laundryDetailText, reset } = params;

    // Reset washing if reset flag is true or no type provided
    if (reset || !type) {
      setWashingStore(null);
      getPrice();
      return;
    }

    if (!data) return;

    let dataWashing = washing?.dataV2 || [];
    const existingIndex = dataWashing.findIndex((e) => e.type === data.type);

    if (existingIndex !== -1) {
      if (quantity === 0) {
        dataWashing = dataWashing.filter((e) => e.type !== data.type);
      } else {
        dataWashing[existingIndex].quantity = quantity || 1;
      }
    } else if (quantity && quantity > 0) {
      dataWashing.push({ type: data.type, quantity, price: data.price });
    }

    setWashingStore({
      type,
      text: laundryDetailText || ({} as IObjectText),
      dataV2: dataWashing,
    });

    getPrice();
  };

  const updateDryClean = async (params: UpdateDryCleanParams) => {
    const { type, data, text, reset } = params;

    // Reset dry clean if reset flag is true or no type provided or data is empty
    if (reset || !type || isEmpty(data) || !text) {
      setDryCleanStore(null);
      getPrice();
      return;
    }

    const dryCleanObj: LaundryDryCleanData = {
      type,
      text,
      data: data || [],
    };

    setDryCleanStore(dryCleanObj);
    getPrice();
  };

  const updateOtherText = async (params: UpdateOtherTextParams) => {
    const { type, data, text, reset } = params;

    // Reset if reset flag is true or no type provided
    if (reset || !type) {
      setOthersStore(null);
      getPrice();
      return;
    }

    const objData: LaundryOtherData = {
      type,
      text: text || ({} as IObjectText),
      data: data || [],
    };

    setOthersStore(objData);
    getPrice();
  };

  const updateOther = async (params: UpdateOtherParams) => {
    const { type, data, quantity, laundryDetailText, reset } = params;
    const currentState = usePostTaskStore.getState();
    const { others } = currentState;
    // Reset Other if no type
    if (reset || !type) {
      setOthersStore(null);
      getPrice();
      return;
    }

    if (!data) return;

    let dataOther = others?.dataV2 || [];
    const selectedValue = dataOther.findIndex((e) => e.type === data.type);

    // Update existing item
    if (selectedValue !== -1) {
      // Remove if quantity is 0
      if (quantity === 0) {
        dataOther = dataOther.filter((e) => e.type !== data.type);
      } else {
        // Update quantity
        dataOther[selectedValue].quantity = quantity || 1;
      }
    } else {
      // Add new item
      dataOther.push({ type: data.type, quantity: 1 });
    }

    const objData: LaundryOtherData = {
      type,
      text: laundryDetailText || ({} as IObjectText),
      dataV2: dataOther,
    };

    setOthersStore(objData);
    getPrice();
  };

  const onChangeCollectionDate = (newDate: IDate) => {
    const currentState = usePostTaskStore.getState();
    const { collectionDate, date } = currentState;
    const isSame = DateTimeHelpers.checkIsSame({
      firstDate: collectionDate as ConfigType,
      secondDate: newDate as ConfigType,
      timezone,
    });
    // check spam, call api with same data
    if (isSame) return;

    setCollectionDate(newDate);

    // check days, If the date of collection is greater than the date of return,
    // Update receipt date next 1 day
    const diffDay = DateTimeHelpers.diffDate({
      timezone,
      firstDate: date as ConfigType,
      secondDate: newDate as ConfigType,
      unit: 'day',
    });
    if (diffDay <= 0) {
      const newReturnDate = DateTimeHelpers.toDateTz({
        timezone,
        date: newDate,
      })
        .add(1, 'day')
        .toDate();
      setDateTime(newReturnDate);
    }

    getPrice();
  };

  return {
    onChangeDuration,
    onChangeDateTime,
    updateWashing,
    updateDryClean,
    updateOtherText,
    updateOther,
    onChangeCollectionDate,
  };
};
