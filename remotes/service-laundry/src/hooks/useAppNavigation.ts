/* eslint-disable no-restricted-imports */
import { useNavigation } from '@react-navigation/native';

import { MainStackParamList, NavigationProps } from '@navigation/type';

/**
 * Custom navigation hook for service-laundry app
 * Purpose: Provides type-safe navigation with proper typing for the main stack navigator
 * @returns {NavigationProps} Typed navigation object for navigating between laundry service screens
 */
export const useAppNavigation = () => {
  return useNavigation<NavigationProps<keyof MainStackParamList>>();
};
