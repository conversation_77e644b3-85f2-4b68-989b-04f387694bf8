import { useCallback, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  DateTimeHel<PERSON>,
  EndpointKeys,
  handleError,
  i18n,
  IApiError,
  useApiMutation,
  useAppLoadingStore,
  useAppStore,
} from '@btaskee/design-system';
import { IDataBooking, IParamsGetPrice } from '@types';
import { get, isEmpty } from 'lodash-es';

import { RouteName } from '@navigation/RouteName';
import { usePostTaskStore } from '@stores';

// Import types
import { ExtendedTaskData } from '../types/task';
// Import utilities
import {
  buildDetailLaundryData,
  buildTaskPaymentData,
  validateLaundryData,
} from '../utils/taskDataBuilders';
import { useAppNavigation } from './useAppNavigation';

/**
 * Enhanced hook for managing laundry task operations
 * with improved error handling and performance optimizations
 *
 * @returns {Object} Hook methods for task management
 * @returns {Function} getPrice - Get pricing for current task configuration
 * @returns {Function} postTask - Submit task for booking
 */
export const usePostTask = () => {
  const { setPrice, setLoadingPrice, setDuration, resetState } =
    usePostTaskStore();
  const { showLoading, hideLoading } = useAppLoadingStore();
  const navigation = useAppNavigation();

  // Memoize API mutations to prevent unnecessary re-renders
  const { mutate: getPriceLaundry } = useApiMutation({
    key: EndpointKeys.getPriceLaundry,
    options: {
      onMutate: () => {
        setLoadingPrice(true);
        showLoading();
      },
      onSettled: () => {
        setLoadingPrice(false);
        hideLoading();
      },
    },
  });

  const { mutate: postTaskLaundry } = useApiMutation({
    key: EndpointKeys.postTaskLaundry,
    options: {
      onSuccess: (data: any) => {
        if (data?.bookingId) {
          resetState();
          navigation.popToTop();
          navigation.navigate(RouteName.PostTaskSuccess);
        }
      },
      onError: (error: IApiError) => {
        handleError(error);
      },
      onMutate: () => {
        showLoading();
      },
      onSettled() {
        hideLoading();
      },
    },
  });

  const { mutate: checkTaskSameTime, data: checkTaskSameTimeData } =
    useApiMutation({
      key: EndpointKeys.checkTaskSameTime,
    });

  // Memoize pricing data building for performance
  const buildPricingData = useCallback((): IParamsGetPrice | null => {
    try {
      const currentState = usePostTaskStore.getState();
      const currentAppState = useAppStore.getState();
      const {
        address,
        date,
        duration,
        service,
        paymentMethod,
        washing,
        dryClean,
        others,
        otherText,
        city,
      } = currentState;
      const { isoCode } = currentAppState;

      if (!address || !date) {
        return null;
      }

      const timezone = DateTimeHelpers.getTimezoneByCity(address.city);
      const laundryData = {
        washing,
        dryClean,
        others,
        otherText,
        city,
      };

      // Validate laundry data before processing
      if (!validateLaundryData(laundryData)) {
        return null;
      }

      const detailLaundryData = buildDetailLaundryData(laundryData);

      const task: IParamsGetPrice['task'] = {
        timezone,
        date: DateTimeHelpers.formatToString({ timezone, date }),
        autoChooseTasker: true,
        taskPlace: {
          country: address.country,
          city: address.city,
          district: address.district,
        },
        homeType: address.homeType,
        duration,
        detailLaundry: detailLaundryData,
      };

      if (paymentMethod?.value) {
        task.payment = {
          method: paymentMethod.value,
        };
      }

      return {
        task,
        service: { _id: service?._id },
        isoCode: isoCode as string,
      };
    } catch (error) {
      return null;
    }
  }, []);

  // Memoize task data building
  const buildTaskData = useCallback((): IDataBooking & ExtendedTaskData => {
    const currentState = usePostTaskStore.getState();
    const currentAppState = useAppStore.getState();
    const {
      address,
      date,
      duration,
      service,
      paymentMethod,
      isApplyNoteForAllTask,
      note,
      isFavouriteTasker,
      relatedTask,
      washing,
      dryClean,
      others,
      otherText,
      city,
      collectionDate,
    } = currentState;
    const { isoCode } = currentAppState;
    const user = (currentAppState as any)?.user;

    const timezone = DateTimeHelpers.getTimezoneByCity(address.city);
    const detailLaundryData = buildDetailLaundryData({
      washing,
      dryClean,
      others,
      otherText,
      city,
    });

    const task: IDataBooking & ExtendedTaskData = {
      address: address.address,
      contactName: address.contact || user?.name || '',
      lat: address.lat,
      lng: address.lng,
      phone: address.phoneNumber || user?.phone || '',
      countryCode: address.countryCode || user?.countryCode || '',
      description: address.description,
      askerId: user?._id || '',
      autoChooseTasker: true,
      date: date ? DateTimeHelpers.formatToString({ date, timezone }) : '',
      timezone,
      duration,
      homeType: address.homeType,
      houseNumber: address.description,
      isoCode: isoCode || '',
      payment: buildTaskPaymentData(paymentMethod),
      serviceId: service?._id || '',
      taskPlace: {
        country: address.country,
        city: address.city,
        district: address.district,
      },
      shortAddress: address.shortAddress,
      isSendToFavTaskers: isFavouriteTasker,
      updateTaskNoteToUser: isApplyNoteForAllTask,
      detailLaundry: detailLaundryData,
    } as IDataBooking & ExtendedTaskData;

    // Add collection date for laundry service
    if (collectionDate) {
      (task as any).collectionDate = DateTimeHelpers.formatToString({
        timezone,
        date: collectionDate,
      });
    }

    // Add optional fields
    if (address?.isAddressMaybeWrong) {
      task.taskPlace!.isAddressMaybeWrong = true;
    }

    if (service?.isTet) {
      task.isTetBooking = true;
    }

    if (note?.trim()) {
      task.taskNote = note.trim();
    }

    if (!isEmpty(relatedTask?.relatedTaskId)) {
      task.source = {
        from: relatedTask?.service?.name || '',
        taskId: relatedTask?.relatedTaskId || '',
      };
    }

    return task;
  }, []);

  /**
   * Get price for laundry service with error handling
   * @returns {Promise<void>} Promise that resolves when pricing is complete
   */
  const getPrice = useCallback(async (): Promise<void> => {
    try {
      const pricingData = buildPricingData();

      if (!pricingData) {
        setPrice(null);
        return;
      }

      setLoadingPrice(true);

      await getPriceLaundry(pricingData, {
        onSuccess: (result: any) => {
          setPrice(result);
          setDuration(result?.duration || 0);
        },
        onError: (error: IApiError) => {
          handleError(error);
          setPrice(null);
        },
      });
    } catch (error) {
      setPrice(null);
    } finally {
      setLoadingPrice(false);
    }
  }, [
    buildPricingData,
    getPriceLaundry,
    setPrice,
    setDuration,
    setLoadingPrice,
  ]);

  /**
   * Execute task posting with error handling
   */
  const executeTaskPosting = useCallback(async (): Promise<any> => {
    try {
      const taskData = buildTaskData();
      return postTaskLaundry(taskData);
    } catch (error) {
      handleError(error as IApiError);
    }
  }, [buildTaskData, postTaskLaundry]);

  /**
   * Handle same time conflict dialog
   */
  const handleSameTimeConflict = useCallback(
    async (callback?: () => void): Promise<void> => {
      Alert.alert?.open({
        title: i18n.t('DIALOG_TITLE_INFORMATION'),
        message: i18n.t('TASK_SAME_TIME_MESSAGE'),
        actions: [
          { text: i18n.t('CLOSE'), style: 'cancel' },
          {
            text: i18n.t('OK'),
            onPress: async () => {
              setTimeout(async () => {
                try {
                  const result = await executeTaskPosting();
                  if (get(result, 'bookingId', null)) {
                    callback?.();
                  }
                } catch (error) {}
              }, 300);
            },
          },
        ],
      });
    },
    [executeTaskPosting],
  );

  /**
   * Post task with same time checking
   * @param {Function} [callback] - Optional callback to execute on success
   * @returns {Promise<any>} Promise that resolves with booking result
   */
  const postTask = useCallback(
    async (callback?: () => void): Promise<any> => {
      try {
        const currentState = usePostTaskStore.getState();
        const { date, service, address } = currentState;

        if (!date || !service?._id || !address) {
          return;
        }

        const timezone = DateTimeHelpers.getTimezoneByCity(address.city);

        // Check for conflicting tasks at the same time
        checkTaskSameTime({
          taskDate: DateTimeHelpers.formatToString({ date, timezone }),
          serviceId: service._id,
        });

        if (checkTaskSameTimeData === false) {
          const result = await executeTaskPosting();

          if (get(result, 'bookingId', null)) {
            callback?.();
          }
          return result;
        }

        // Handle same time conflict
        return handleSameTimeConflict(callback);
      } catch (error) {
        handleError(error as IApiError);
      }
    },
    [
      executeTaskPosting,
      handleSameTimeConflict,
      checkTaskSameTime,
      checkTaskSameTimeData,
    ],
  );

  // Memoize the return object to prevent unnecessary re-renders
  return useMemo(
    () => ({
      getPrice,
      postTask,
    }),
    [getPrice, postTask],
  );
};
