/**
 * Styles for the IntroService screen
 */
import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorsV2.neutralBackground,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.SPACE_16,
  },
  contentContainerStyle: {
    flexGrow: 1,
    marginTop: Spacing.SPACE_20,
  },
  wrapNote: {
    paddingBottom: Spacing.SPACE_40,
  },
  txtHeader: {
    fontSize: FontSizes.SIZE_14,
    color: ColorsV2.neutral700,
    marginBottom: Spacing.SPACE_16,
  },
  txtServiceName: {
    fontSize: FontSizes.SIZE_24,
    color: ColorsV2.neutral900,
    lineHeight: FontSizes.SIZE_20,
    marginBottom: Spacing.SPACE_12,
  },
  boxText: {
    marginTop: Spacing.SPACE_16,
  },
  wrapMarginTop: {
    paddingLeft: Spacing.SPACE_12,
  },
  wrapBottom: {
    paddingVertical: Spacing.SPACE_16,
    marginHorizontal: Spacing.SPACE_16,
    marginBottom: Spacing.SPACE_16,
    backgroundColor: ColorsV2.orange500,
    borderRadius: BorderRadius.RADIUS_08,
    alignItems: 'center',
  },
  titleBtn: {
    fontSize: FontSizes.SIZE_16,
    color: ColorsV2.neutralWhite,
  },
});
