import React from 'react';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  IUser,
  Markdown,
  ScrollView,
  TouchableOpacity,
} from '@btaskee/design-system';
import { get } from 'lodash-es';

import { useI18n } from '@hooks';
import { RouteName } from '@navigation/RouteName';

import { styles } from './styles';

const TextItem = ({ title }: { title: string }) => {
  return (
    <BlockView
      row
      horizontal
      style={styles.wrapMarginTop}>
      <BlockView
        flex
        style={styles.boxText}>
        <CText style={styles.txtHeader}>{title}</CText>
      </BlockView>
    </BlockView>
  );
};

type IntroServiceProps = {
  navigation: any; // Navigation type would need to be imported from navigation library
  user: IUser;
  route: {
    params?: {
      isHideButton?: boolean;
    };
  };
};

export const IntroService: React.FC<IntroServiceProps> = ({
  navigation,
  user,
  route,
}) => {
  const { t } = useI18n();

  const isHideButton = route?.params?.isHideButton;

  const setAddressToReducer = async (address: any = {}) => {
    // Implementation for setting address
  };

  const onSubmit = async () => {
    // Get first address
    let addressList: any[] = get(user, 'locations', []) || [];
    addressList = addressList.filter((e: any) => {
      return e.isoCode === user?.isoCode;
    });

    if (addressList && addressList.length > 0) {
      // Use default address
      let address = addressList.find((item: any) => item?.isDefault === true);
      if (!address) {
        address = addressList[0];
      }
      await setAddressToReducer(address);
      return navigation.replace(RouteName.ChooseAddress);
    }
    return navigation.replace(RouteName.ChooseAddress);
  };

  const onBack = () => navigation.goBack();

  return (
    <BlockView
      inset={['bottom']}
      style={styles.container}>
      <BlockView flex>
        <BlockView style={styles.content}>
          <ScrollView
            style={styles.wrapNote}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.contentContainerStyle}>
            <CText
              bold
              style={styles.txtServiceName}>
              {t('LAUNDRY_INTRO_TITLE')}
            </CText>
            <Markdown
              textStyle={styles.txtHeader}
              text={t('LAUNDRY_INTRO_DESCRIPTION')}
            />
            <TextItem title={t('LAUNDRY_INTRO_FEATURE_1')} />
            <TextItem title={t('LAUNDRY_INTRO_FEATURE_2')} />
            <TextItem title={t('LAUNDRY_INTRO_FEATURE_3')} />
            <TextItem title={t('LAUNDRY_INTRO_FEATURE_4')} />
          </ScrollView>
        </BlockView>
      </BlockView>
      <ConditionView
        condition={Boolean(isHideButton)}
        viewFalse={
          <TouchableOpacity
            style={styles.wrapBottom}
            onPress={onSubmit}>
            <CText
              bold
              style={styles.titleBtn}
              color={ColorsV2.neutralWhite}>
              {t('INTRO_START_EXPERIENCE')}
            </CText>
          </TouchableOpacity>
        }
        viewTrue={
          <TouchableOpacity
            style={styles.wrapBottom}
            onPress={onBack}>
            <CText
              bold
              style={styles.titleBtn}
              color={ColorsV2.neutralWhite}>
              {t('BTN_BACK')}
            </CText>
          </TouchableOpacity>
        }
      />
    </BlockView>
  );
};
