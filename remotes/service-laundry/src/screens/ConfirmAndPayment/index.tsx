import React, { useCallback, useRef, useState } from 'react';
import {
  <PERSON><PERSON>,
  BlockView,
  BookingButton,
  CModal,
  CModalHandle,
  ColorsV2,
  ConditionView,
  CText,
  DatePicker,
  DateTimeHelpers,
  FontSizes,
  LocationPostTask,
  PaymentDetail,
  PaymentDetailStep4WithDateOptions,
  PaymentMethod,
  ScrollView,
  SizedBox,
  Spacing,
  TimePicker,
  TouchableOpacity,
  useSettingsStore,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useAppNavigation, useI18n, usePostTask } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { usePostTaskStore } from '@stores';

import { TaskDetail } from '../../components/TaskDetail';
import { styles } from './styles';

export const ConfirmAndPayment = () => {
  const { t } = useI18n();

  const { getPrice, postTask } = usePostTask();
  const {
    setAddress,
    service,
    date,
    setDateTime,
    address,
    homeNumber,
    paymentMethod,
    promotion,
    setPromotion,
    price,
  } = usePostTaskStore();
  const { settings } = useSettingsStore();

  const navigation = useAppNavigation();
  // const { setPaymentMethodWhenBooking } = useBookTask();
  // const { trackingNextBackActionPostTaskStep4 } = useAppTracking();

  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);
  const [isTet, setIsTet] = useState(service?.isTet);
  const [selectedDate, setSelectedDate] = useState(date);
  const modalRef = useRef<CModalHandle | null>(null);
  // const refModalLogin = useRef<Modalize | null>(null);

  const removeTetInService = () => {
    // Clear isTet in service
    delete service?.isTet;
    // setService(service);
    setIsTet(false);
    // setPaymentMethodWhenBooking();
  };

  const _changeToRegularBooking = () => {
    // Require change date if the choosen date is over regular range
    const maxDate = DateTimeHelpers.toDayTz({ timezone })
      .add(6, 'day')
      .endOf('date');
    const isAfter = DateTimeHelpers.checkIsAfter({
      timezone,
      firstDate: date,
      secondDate: maxDate,
    });
    if (isAfter) {
      // Set default date is 2PM tomorrow
      const tomorrow = DateTimeHelpers.toDayTz({ timezone })
        .add(1, 'day')
        .hour(14)
        .startOf('hour');
      setSelectedDate(tomorrow.toDate());

      // Show change new date modal
      modalRef?.current?.open && modalRef?.current?.open();
    } else {
      removeTetInService();
    }
  };

  // update date time when user change
  const onChangeDateTime = (dateObj) => {
    // check spam, call api with same data
    const isSame = DateTimeHelpers.checkIsSame({
      timezone,
      firstDate: date,
      secondDate: dateObj,
    });
    if (isSame) return null;

    setSelectedDate(dateObj);
  };

  const _changeNewDate = async () => {
    setDateTime(selectedDate);
    // recaculate duration and estimated time, only Home Cooking service
    getPrice();
    removeTetInService();
  };

  const removePromotion = async () => {
    await setPromotion(null);
    getPrice();
  };

  // const _resetStateAfterPTSuccess = () => {
  // resetAllState();
  // resetStateStep2();
  // resetStateStep4();
  // };

  // const _openModalLogin = () => {
  //   refModalLogin?.current?.open && refModalLogin?.current?.open();
  // };

  const getOutstandingPayment = async () => {
    // Check exist userId
    // if (!global.userId) {
    //   return;
    // }
    // // Fetch the Outstanding payment debt
    // const outstandingDebt = await getOutstandingPaymentDebt({});
    // if (outstandingDebt?.isSuccess && outstandingDebt?.data?.length > 0) {
    //   // If user has any outstanding payment, redirect user to out standing payment debt page
    //   return navigation.navigate(RouteName.OutstandingPayment, {
    //     outstanding: outstandingDebt.data,
    //   });
    // }
    // Do nothing
  };
  const _onPosTask = async () => {
    // trackingNextBackActionPostTaskStep4({
    //   serviceName: SERVICES.AIR_CONDITIONER,
    //   action: TRACKING_ACTION.Next,
    // });

    const response = await postTask(postTaskCallBack);
    if (response && response?.code === 'OUTSTANDING_PAYMENT_STATUS_NEW') {
      Alert?.alert?.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('OUTSTANDING_PAYMENT_STATUS_NEW'),
        actions: [
          {
            text: t('PAYMENT_TOP_UP'),
            onPress: () => {
              getOutstandingPayment();
            },
          },
        ],
      });
      return;
    }
  };
  /**
   * Callback after successful task posting
   */
  const postTaskCallBack = useCallback(() => {
    if (navigation) {
      navigation.popToTop();
      navigation.navigate(RouteName.PostTaskSuccess);
    }
  }, [navigation]);

  return (
    <BlockView style={styles.container}>
      <BlockView flex>
        <ScrollView
          scrollIndicatorInsets={{ right: 1 }}
          testID="scrollViewStep4"
          style={styles.content}>
          <LocationPostTask
            address={address}
            homeNumber={homeNumber}
            setAddress={setAddress}
          />

          <TaskDetail />

          <ConditionView
            condition={!isEmpty(price?.dateOptions)}
            viewTrue={
              <PaymentDetailStep4WithDateOptions
                dateOptions={price?.dateOptions}
                timezone={timezone}
                paymentMethod={paymentMethod}
              />
            }
            viewFalse={<PaymentDetail price={price} />}
          />

          <PaymentMethod
            isTet={isTet}
            removePromotion={removePromotion}
            paymentMethod={paymentMethod}
            promotion={promotion}
            setPromotion={setPromotion}
          />
          <ConditionView
            condition={Boolean(isTet)}
            viewTrue={
              <TouchableOpacity
                style={styles.btn}
                onPress={() => _changeToRegularBooking()}>
                <CText
                  center
                  bold
                  style={{
                    color: ColorsV2.orange500,
                    fontSize: FontSizes.SIZE_16,
                  }}>
                  {t('TET_BOOKING_TO_NORMAL_TASK')}
                </CText>
                <CText
                  center
                  size={FontSizes.SIZE_16}
                  margin={{ top: Spacing.SPACE_04 }}>
                  {t('TET_BOOKING_TO_NORMAL_TASK_DESCRIPTION')}
                </CText>
              </TouchableOpacity>
            }
          />
          <ConditionView
            condition={Boolean(isTet)}
            viewTrue={
              <CModal
                hideButtonClose
                ref={modalRef}
                title={t('TET_BOOKING_TO_NOMAL_NOTE_TITLE')}
                actions={[
                  {
                    text: t('TET_BOOKING_TO_NOMAL_NOTE_DONE'),
                    onPress: _changeNewDate,
                    disabled: DateTimeHelpers.checkIsSame({
                      timezone,
                      firstDate: date,
                      secondDate: selectedDate,
                    }),
                  },
                ]}>
                <CText>{t('TET_BOOKING_TO_NORMAL_TASK_CHANGE_DATE')}</CText>
                <BlockView>
                  <DatePicker
                    title={t('STEP_4_UPDATE_CALENDAR_TITLE')}
                    value={selectedDate}
                    onChange={onChangeDateTime}
                    settingSystem={settings?.settingSystem}
                    timezone={timezone}
                  />

                  <TimePicker
                    noShowTitle={false}
                    title={t('STEP_4_UPDATE_TIME_TITLE')}
                    value={selectedDate}
                    onChange={onChangeDateTime}
                    settingSystem={settings?.settingSystem}
                    timezone={timezone}
                  />
                </BlockView>
              </CModal>
            }
          />
          <SizedBox style={styles.styleBottom} />
        </ScrollView>
      </BlockView>
      <BookingButton
        price={price}
        navigation={navigation}
        onPostTask={_onPosTask}
      />
      {/* <ModalLogin
        ref={refModalLogin}
        callback={_callback} // call when login success
        routeName={{ routeName: 'Tab_Activity', screen: 'Tab_Upcoming' }} // After sign in success will navigate to this route
      /> */}
    </BlockView>
  );
};
