import React, { useCallback, useEffect, useMemo } from 'react';
import { BlockView, ScrollView } from '@btaskee/design-system';

import { DetailItem } from '../DetailItem';
import { styles } from './styles';

interface DryCleaningDetailListProps {
  cleanTypeChoose: any;
  dryCleaningChoose: any[];
  updateDryClean: (
    cleaningDetail: any,
    quantity: number,
    indexChoose: number,
    dryCleaningChoose: any[],
  ) => void;
  setIsRenderDetail: (value: boolean) => void;
  isRenderDetail: boolean;
}

export const DryCleaningDetailList = ({
  cleanTypeChoose,
  dryCleaningChoose,
  updateDryClean,
  setIsRenderDetail,
  isRenderDetail,
}: DryCleaningDetailListProps) => {
  // Memoized clean type detail data
  const cleanTypeDetail = useMemo(
    () => cleanTypeChoose?.data || [],
    [cleanTypeChoose?.data],
  );

  // Effect to handle render detail timing
  useEffect(() => {
    let timeout: ReturnType<typeof setTimeout>;

    if (cleanTypeChoose) {
      timeout = setTimeout(() => {
        setIsRenderDetail(true);
      }, 50);
    }

    return () => {
      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }, [cleanTypeChoose, setIsRenderDetail]);

  // Memoized update function for better performance
  const handleUpdateDryClean = useCallback(
    (type: any, cleaningDetail: any, value: any) => {
      // Find existing item index in dry cleaning array
      const indexChoose = dryCleaningChoose.findIndex(
        (item) => item.type === cleaningDetail.type,
      );

      updateDryClean(cleaningDetail, value, indexChoose, dryCleaningChoose);
    },
    [updateDryClean, dryCleaningChoose],
  );

  // Early return if conditions are not met
  if (!cleanTypeChoose || !isRenderDetail) {
    return null;
  }

  return (
    <BlockView style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}>
        {cleanTypeDetail.map((data: any) => (
          <DetailItem
            key={data.type}
            cleaningDetail={data}
            onChangeValue={handleUpdateDryClean}
            data={dryCleaningChoose}
            type="dryClean"
          />
        ))}
      </ScrollView>
    </BlockView>
  );
};
