import { StyleSheet } from 'react-native';
import { ColorsV2, FontSizes, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  boxLeftCleaningDetail: {
    flex: 1,
  },

  boxRightCleaningDetail: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'center',
  },

  txtTitleDetail: {
    color: ColorsV2.neutral500,
    fontSize: FontSizes.SIZE_14,
    paddingVertical: Spacing.SPACE_04,
  },

  txtPriceDetail: {
    color: ColorsV2.orange500,
    fontSize: FontSizes.SIZE_12,
    paddingVertical: Spacing.SPACE_04,
  },

  rowCleaningItem: {
    paddingVertical: Spacing.SPACE_04,
  },
});
