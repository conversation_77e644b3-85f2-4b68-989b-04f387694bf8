import React, { useCallback, useMemo } from 'react';
import {
  BlockView,
  CText,
  getTextWithLocale,
  IObjectText,
  ISO_CODE,
  showPriceAndCurrency,
  useAppStore,
} from '@btaskee/design-system';
import { debounce, isEmpty } from 'lodash-es';

import { NumberSpinner } from '@components';

import { LaundryWashingItem } from '../../../../types/task';
import { styles } from './styles';

const MAX_QUANTITY_CLOTHES = 20; // Maximum quantity for washing
const MIN_MAX_QUANTITY_CLOTHES = 3; // Min quantity for washing
const MIN_MAX_QUANTITY_CLOTHES_INDO = 5; // Min quantity for washing

interface CleaningDetailItem {
  type: string;
  text: IObjectText;
  price: number;
  unit?: IObjectText;
}

interface DetailItemProps {
  cleaningDetail: CleaningDetailItem;
  onChangeValue: (
    type: string,
    cleaningDetail: CleaningDetailItem,
    value: number,
  ) => void;
  data: LaundryWashingItem[];
  type: string;
  index?: number; // Index for staggered animation
  isFromOtherService?: boolean;
}

export const DetailItem: React.FC<DetailItemProps> = ({
  cleaningDetail,
  onChangeValue,
  data,
  type,
  index = 0,
  isFromOtherService = false,
}) => {
  const { isoCode } = useAppStore();

  const minQuantityClothes = useMemo(
    () =>
      isoCode === ISO_CODE.ID
        ? MIN_MAX_QUANTITY_CLOTHES_INDO
        : MIN_MAX_QUANTITY_CLOTHES,
    [isoCode],
  );

  const cleaningName = useMemo(
    () => getTextWithLocale(cleaningDetail?.text),
    [cleaningDetail?.text],
  );

  const cleaningPrice = useMemo(
    () => cleaningDetail.price || 0,
    [cleaningDetail.price],
  );

  const unit = useMemo(
    () =>
      (cleaningDetail?.unit ? getTextWithLocale(cleaningDetail.unit) : null),
    [cleaningDetail?.unit],
  );

  const formattedPrice = useMemo(
    () => `${showPriceAndCurrency(cleaningPrice)}${unit ? `/${unit}` : ''}`,
    [cleaningPrice, unit],
  );

  // Find index in washing data array
  const indexChoose = useMemo(() => {
    if (isEmpty(data)) {
      return -1;
    }
    return data.findIndex(
      (e: LaundryWashingItem) => e.type === cleaningDetail.type,
    );
  }, [data, cleaningDetail.type]);

  const cleaningAmount = useMemo(
    () => (indexChoose !== -1 ? data[indexChoose].quantity : 0),
    [indexChoose, data],
  );

  const _onChangeValue = useCallback(
    (value: number) => {
      onChangeValue(type, cleaningDetail, value);
    },
    [onChangeValue, type, cleaningDetail],
  );

  // Debounced value change to avoid spam updates
  const _onChangeValueDebounce = useMemo(
    () => debounce(_onChangeValue, 350),
    [_onChangeValue],
  );

  return (
    <BlockView
      horizontal
      row
      style={styles.rowCleaningItem}>
      <BlockView style={styles.boxLeftCleaningDetail}>
        <CText style={styles.txtTitleDetail}>{cleaningName}</CText>
        <CText style={styles.txtPriceDetail}>{formattedPrice}</CText>
      </BlockView>

      <BlockView style={styles.boxRightCleaningDetail}>
        <NumberSpinner
          maxValue={MAX_QUANTITY_CLOTHES}
          minValue={isFromOtherService ? minQuantityClothes : 0}
          value={cleaningAmount}
          onValueChange={_onChangeValueDebounce}
          testID={cleaningName}
        />
      </BlockView>
    </BlockView>
  );
};
