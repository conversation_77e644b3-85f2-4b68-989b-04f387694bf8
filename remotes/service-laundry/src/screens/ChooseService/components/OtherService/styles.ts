import { StyleSheet } from 'react-native';
import { ColorsV2, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  card: {
    padding: Spacing.SPACE_16,
    marginTop: Spacing.SPACE_16,
    backgroundColor: ColorsV2.neutralWhite,
  },

  title: {
    color: ColorsV2.neutral900,
    fontSize: Spacing.SPACE_16,
    flex: 1,
  },

  checkboxContainer: {
    padding: 0,
    marginLeft: 0,
    marginRight: 0,
  },

  checkboxText: {
    marginLeft: 0,
    marginRight: 0,
  },

  otherSection: {
    marginTop: Spacing.SPACE_08,
  },

  otherLabel: {
    marginTop: Spacing.SPACE_16,
    fontSize: 14,
    color: ColorsV2.neutral500,
  },

  textInput: {
    fontSize: 14,
    minHeight: 65,
    color: ColorsV2.neutral900,
  },

  textInputContainer: {
    paddingHorizontal: 0,
    paddingVertical: 0,
    marginTop: Spacing.SPACE_08,
  },

  description: {
    marginTop: Spacing.SPACE_16,
    fontStyle: 'italic',
    fontSize: 12,
    color: ColorsV2.neutral500,
    lineHeight: Spacing.SPACE_20,
  },
});
