import React, { useCallback, useMemo, useState } from 'react';
import {
  AnimationHelpers,
  BlockView,
  Card,
  CheckBox,
  CText,
  CTextInput,
  getTextWithLocale,
} from '@btaskee/design-system';
import { debounce, get } from 'lodash-es';

import { useI18n } from '@hooks';

import { useChangeData } from '../../../../hooks/useChangeData';
import { DetailItem } from '../DetailItem';
import { styles } from './styles';

interface OtherServiceProps {
  others: any;
  laundryDetail: any;
  type: string;
  otherText: string;
}

export const OtherService = ({
  others,
  laundryDetail,
  type,
  otherText,
}: OtherServiceProps) => {
  const { t } = useI18n();
  const { updateOther, updateOtherText } = useChangeData();

  // Local state management
  const [otherContent] = useState(otherText);
  const [isWashing, setIsWashing] = useState(<PERSON>olean(others || otherText));

  // Memoized values for better performance
  const cleanTypeDetail = useMemo(
    () => get(laundryDetail, 'dataV2', []) || [],
    [laundryDetail],
  );

  const laundryText = useMemo(
    () => getTextWithLocale(laundryDetail?.text),
    [laundryDetail?.text],
  );

  const _onChangeText = useCallback(
    async (text: string) => {
      await updateOtherText({
        type,
        data: { text },
        text: laundryDetail?.text,
      });
    },
    [updateOtherText, type, laundryDetail?.text],
  );

  const _debounce = useMemo(
    () => debounce(_onChangeText, 1000),
    [_onChangeText],
  );

  // Handlers
  const _onChangeOther = useCallback(
    async (itemType: string, cleaningDetail: any, value: any) => {
      await updateOther({
        type: itemType,
        data: cleaningDetail,
        quantity: value,
        laundryDetailText: laundryDetail?.text,
      });
    },
    [updateOther, laundryDetail?.text],
  );

  const _onChooseWashing = useCallback(
    async (isChoose: boolean) => {
      AnimationHelpers.runLayoutAnimation();
      setIsWashing(isChoose);

      if (!isChoose) {
        // Reset other services when unchecked
        await updateOther({ reset: true });
        await updateOtherText({ reset: true });
        return;
      }

      // No need to call getPrice here since updateOther/updateOtherText already call it
      // when user interacts with form elements
    },
    [updateOther, updateOtherText],
  );

  return (
    <Card style={styles.card}>
      <BlockView
        horizontal
        row
        jBetween>
        <CText
          bold
          style={styles.title}>
          {laundryText}
        </CText>
        <CheckBox
          testID="switchOther"
          containerStyle={styles.checkboxContainer}
          textStyle={styles.checkboxText}
          onChecked={_onChooseWashing}
          checked={isWashing}
        />
      </BlockView>

      {isWashing && (
        <>
          {cleanTypeDetail.map((data: any) => (
            <DetailItem
              key={data.type}
              cleaningDetail={data}
              onChangeValue={_onChangeOther}
              data={others?.dataV2}
              type={type}
              isFromOtherService={true}
            />
          ))}

          <BlockView style={styles.otherSection}>
            <CText style={styles.otherLabel}>{t('OTHER')}</CText>
            <CTextInput
              testID="inputOther"
              defaultValue={otherContent}
              onChangeText={_debounce}
              multiline
              inputStyle={styles.textInput}
              placeholder={t('PLACE_HOLDER_OTHER_LAUNDRY')}
              containerStyle={styles.textInputContainer}
              maxLength={400}
            />
          </BlockView>
        </>
      )}
    </Card>
  );
};

// Named export already exists above - export const OtherService
