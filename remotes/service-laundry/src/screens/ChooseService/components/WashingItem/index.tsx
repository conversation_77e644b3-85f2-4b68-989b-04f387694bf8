import React, { useCallback, useState } from 'react';
import {
  <PERSON>View,
  Card,
  CheckBox,
  CText,
  getTextWithLocale,
  IObjectText,
} from '@btaskee/design-system';
import { get, isEmpty } from 'lodash-es';

import { useChangeData } from '@hooks';

import { LaundryDetail, LaundryWashingData } from '../../../../types/task';
import { DetailItem } from '../DetailItem';
import { styles } from './styles';

interface CleaningDetailItem {
  type: string;
  text: IObjectText;
  price: number;
  unit?: IObjectText;
}

interface WashingItemProps {
  laundryDetail: LaundryDetail;
  washing: LaundryWashingData | null;
  type: string;
}

export const WashingItem: React.FC<WashingItemProps> = ({
  laundryDetail,
  washing,
  type,
}) => {
  const [isWashing, setIsWashing] = useState(!isEmpty(washing));
  const { updateWashing } = useChangeData();

  // Memoized values
  const laundryTitle = React.useMemo(
    () => getTextWithLocale(laundryDetail?.text),
    [laundryDetail?.text],
  );
  const cleanTypeDetail = React.useMemo(
    () => get(laundryDetail, 'dataV2', []) as CleaningDetailItem[],
    [laundryDetail],
  );

  // Animation helper

  const handleChangeWashing = useCallback(
    (itemType: string, cleaningDetail: CleaningDetailItem, value: number) => {
      updateWashing({
        type: itemType,
        data: cleaningDetail,
        quantity: value,
        laundryDetailText: laundryDetail?.text,
      });
    },
    [updateWashing, laundryDetail?.text],
  );

  const handleToggleWashing = useCallback(
    async (isChoose: boolean) => {
      setIsWashing(isChoose);

      if (!isChoose) {
        await updateWashing({ reset: true });
      }
      // No need to call getPrice when isChoose is true -
      // it will be called when user selects items via handleChangeWashing -> updateWashing
    },
    [updateWashing],
  );

  return (
    <Card style={styles.cardContainer}>
      <BlockView
        jBetween
        horizontal
        row
        style={styles.headerContainer}>
        <CText
          bold
          style={styles.titleText}>
          {laundryTitle}
        </CText>
        <CheckBox
          testID="switchWashing"
          containerStyle={styles.checkboxContainer}
          textStyle={styles.checkboxText}
          onChecked={handleToggleWashing}
          checked={isWashing}
        />
      </BlockView>

      {isWashing && (
        <BlockView style={styles.detailsContainer}>
          {cleanTypeDetail.map((data: CleaningDetailItem, index: number) => (
            <DetailItem
              key={data.type}
              cleaningDetail={data}
              onChangeValue={handleChangeWashing}
              data={washing?.dataV2 || []}
              type={type}
            />
          ))}
        </BlockView>
      )}
    </Card>
  );
};
