import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

export const styles = StyleSheet.create({
  cardContainer: {
    padding: Spacing.SPACE_16,
    marginBottom: Spacing.SPACE_12,
    borderWidth: 1,
    borderColor: ColorsV2.neutral100,
    borderRadius: BorderRadius.RADIUS_08,
  },

  headerContainer: {
    alignItems: 'center',
  },

  titleText: {
    color: ColorsV2.neutral900,
    fontSize: FontSizes.SIZE_16,
    flex: 1,
  },

  checkboxContainer: {
    padding: Spacing.SPACE_0,
    marginLeft: Spacing.SPACE_0,
    marginRight: Spacing.SPACE_0,
  },

  checkboxText: {
    marginLeft: Spacing.SPACE_0,
    marginRight: Spacing.SPACE_0,
  },

  detailsContainer: {
    overflow: 'hidden',
  },
});
