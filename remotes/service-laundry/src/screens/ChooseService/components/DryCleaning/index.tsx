import React, { useCallback, useMemo, useState } from 'react';
import {
  <PERSON><PERSON>iew,
  Card,
  CheckBox,
  CText,
  getTextWithLocale,
} from '@btaskee/design-system';
import { get } from 'lodash-es';

import { useChangeData } from '@hooks';

import { DryCleaningDetail } from '../DryCleaningDetail';
import { styles } from './styles';

interface DryCleaningProps {
  dryCleaningDetail: any;
  dryClean: any;
}

export const DryCleaning = ({
  dryCleaningDetail,
  dryClean,
}: DryCleaningProps) => {
  const { updateDryClean } = useChangeData();

  // State management with proper initialization
  const [dryCleaningChoose, setDryCleaningData] = useState(
    get(dryClean, 'data', []) || [],
  );

  const [isDryCleaning, setIsDryCleaning] = useState(
    get(dryClean, 'data', []).length > 0,
  );

  const [isRenderDetail, setIsRenderDetail] = useState(false);

  // Memoized values for better performance
  const dryCleaningTitle = useMemo(
    () => getTextWithLocale(dryCleaningDetail?.text),
    [dryCleaningDetail?.text],
  );

  const _setDryCleaning = useCallback(
    (data: any[]) => {
      updateDryClean({
        type: dryCleaningDetail.type,
        data,
        text: dryCleaningDetail.text,
      });
      setDryCleaningData(data);
    },
    [dryCleaningDetail, updateDryClean],
  );

  const _updateDryClean = useCallback(
    (
      cleaningDetail: any,
      quantity: number,
      indexChoose: number,
      currentDryCleaningChoose: any[],
    ) => {
      const newDryData = [...currentDryCleaningChoose];
      const newData = {
        type: cleaningDetail.type,
        text: cleaningDetail.text,
        price: cleaningDetail.price,
        quantity: quantity,
      };

      // If item exists, replace it in array
      if (indexChoose !== -1) {
        newDryData.splice(indexChoose, 1);

        if (quantity <= 0) {
          return _setDryCleaning(newDryData);
        }
      }

      _setDryCleaning([...newDryData, newData]);
    },
    [_setDryCleaning],
  );

  const _onActiveDryCleaning = useCallback(
    async (status: boolean) => {
      if (!status) {
        await updateDryClean({ reset: true });
        setIsDryCleaning(status);
        // updateDryClean already handles price calculation internally
      } else if (status && dryCleaningChoose && dryCleaningChoose.length > 0) {
        await updateDryClean({
          type: dryCleaningDetail.type,
          data: dryCleaningChoose,
          text: dryCleaningDetail.text,
        });
        // updateDryClean already handles price calculation internally
      }

      setIsDryCleaning(status);
    },
    [updateDryClean, dryCleaningChoose, dryCleaningDetail],
  );

  return (
    <Card style={styles.cardContainer}>
      <BlockView
        jBetween
        horizontal
        row
        style={styles.headerContainer}>
        <CText
          bold
          style={styles.titleText}>
          {dryCleaningTitle}
        </CText>

        <CheckBox
          testID="switchDryClean"
          containerStyle={styles.checkboxContainer}
          textStyle={styles.checkboxText}
          onChecked={_onActiveDryCleaning}
          checked={isDryCleaning}
        />
      </BlockView>

      <DryCleaningDetail
        isRenderDetail={isRenderDetail}
        setIsRenderDetail={setIsRenderDetail}
        updateDryClean={_updateDryClean}
        isDryCleaning={isDryCleaning}
        dryCleaningChoose={dryCleaningChoose}
        dryDetailArr={dryCleaningDetail.data}
      />
    </Card>
  );
};
