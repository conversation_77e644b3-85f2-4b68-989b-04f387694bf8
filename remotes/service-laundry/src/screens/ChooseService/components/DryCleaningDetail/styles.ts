import { StyleSheet } from 'react-native';
import { FontSizes, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  buttonScrollContainer: {},

  scrollContainer: {
    paddingLeft: Spacing.SPACE_16,
    paddingRight: Spacing.SPACE_16,
    paddingBottom: Spacing.SPACE_16,
    paddingTop: Spacing.SPACE_08,
  },

  imageItemContainer: {
    borderRadius: Spacing.SPACE_08,
  },

  iconDryLaundry: {
    width: 20,
    height: 20,
  },

  icNextPlus: {
    width: 20,
    height: 20,
  },

  amountBadge: {
    top: -10,
    right: 0,
    borderRadius: 20,
  },

  amountBadgeFixed: {
    top: -10,
    right: 0,
    borderRadius: 20,
    minWidth: 20,
    minHeight: 20,
  },

  amountText: {
    fontSize: FontSizes.SIZE_12,
  },

  animatedContainer: {
    overflow: 'hidden',
  },
});
