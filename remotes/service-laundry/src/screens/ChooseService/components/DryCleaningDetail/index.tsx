import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  areEqualMemo,
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  DeviceHelper,
  FastImage,
  getTextWithLocale,
  IconAssets,
  ScrollView,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { find, forEach } from 'lodash-es';

import {
  imgLaundryBlanket,
  imgLaundryDress,
  imgLaundryOther,
  imgLaundryShirt,
  imgLaundrySuit,
  imgLaundryTrousers,
} from '@images';

import { DryCleaningDetailList } from '../DryCleaningDetailList';
import { styles } from './styles';

const { WIDTH: width } = DeviceHelper.WINDOW;

const CurrentScroll = {
  TOP: 'TOP',
  END: 'END',
} as const;

type ScrollPosition = (typeof CurrentScroll)[keyof typeof CurrentScroll];

const IMAGE_DRY_CLEANING = {
  dryClean_1: imgLaundrySuit,
  dryClean_2: imgLaundryShirt,
  dryClean_3: imgLaundryTrousers,
  dryClean_4: imgLaundryDress,
  dryClean_5: imgLaundryBlanket,
} as const;

const TEN_PERCENT = 0.05;
const RANGE = 50;

interface ButtonScrollProps {
  callBack: () => void;
  positionLeft?: number;
  positionRight?: number;
  isRotate?: boolean;
}

const ButtonScroll = memo(
  ({ positionLeft, positionRight, callBack, isRotate }: ButtonScrollProps) => {
    return (
      <BlockView
        positionRight={positionRight}
        positionLeft={positionLeft}
        absolute
        zIndex={100}
        backgroundColor={ColorsV2.neutralWhite}
        style={styles.buttonScrollContainer}
        padding={{ vertical: Spacing.SPACE_16 }}>
        <TouchableOpacity
          onPress={callBack}
          hitSlop={{ top: 25, bottom: 25 }}>
          <FastImage
            source={IconAssets.icArrowRight}
            style={[
              styles.icNextPlus,
              { transform: [{ rotate: isRotate ? '180deg' : '0deg' }] },
            ]}
          />
        </TouchableOpacity>
      </BlockView>
    );
  },
);

interface ImageItemProps {
  data: any;
  onPress: (data: any) => void;
  isChoose: boolean;
  amount?: number;
}

const ImageItem = memo(
  ({ data, onPress, isChoose, amount = 0 }: ImageItemProps) => {
    const iconColor = useMemo(
      () => (isChoose ? ColorsV2.neutralWhite : ColorsV2.neutral900),
      [isChoose],
    );

    const backgroundColor = useMemo(
      () => (isChoose ? ColorsV2.orange500 : ColorsV2.neutral50),
      [isChoose],
    );

    const callBack = useCallback(() => onPress(data), [onPress, data]);

    const displayText = useMemo(
      () => getTextWithLocale(data?.text),
      [data?.text],
    );

    return (
      <TouchableOpacity
        testID={data?.text?.vi}
        onPress={callBack}>
        <BlockView
          center
          row
          padding={{
            horizontal: Spacing.SPACE_16,
            vertical: Spacing.SPACE_08,
          }}
          backgroundColor={backgroundColor}
          margin={{ right: Spacing.SPACE_08 }}
          style={styles.imageItemContainer}>
          <FastImage
            tintColor={iconColor}
            style={styles.iconDryLaundry}
            resizeMode="cover"
            source={
              IMAGE_DRY_CLEANING[
                data.type as keyof typeof IMAGE_DRY_CLEANING
              ] || imgLaundryOther
            }
          />

          <CText
            bold
            center
            margin={{ left: Spacing.SPACE_04 }}
            color={iconColor}>
            {displayText}
          </CText>
        </BlockView>

        {amount > 0 && (
          <BlockView
            center
            absolute
            style={styles.amountBadgeFixed}
            backgroundColor={ColorsV2.red500}
            zIndex={1000}
            padding={{ horizontal: Spacing.SPACE_04 }}>
            <CText
              bold
              center
              style={styles.amountText}
              color={ColorsV2.neutralWhite}>
              {amount}
            </CText>
          </BlockView>
        )}
      </TouchableOpacity>
    );
  },
  areEqualMemo,
);

interface DryCleaningDetailProps {
  dryDetailArr: any[];
  dryCleaningChoose: any[];
  setIsRenderDetail: (value: boolean) => void;
  isRenderDetail: boolean;
  updateDryClean: (
    cleaningDetail: any,
    quantity: number,
    indexChoose: number,
    dryCleaningChoose: any[],
  ) => void;
  isDryCleaning: boolean;
}

export const DryCleaningDetail = ({
  dryDetailArr,
  dryCleaningChoose,
  setIsRenderDetail,
  isRenderDetail,
  updateDryClean,
  isDryCleaning,
}: DryCleaningDetailProps) => {
  const [cleanTypeChoose, setCleanTypeChoose] = useState<any>();
  const scrollViewRef = useRef<React.ElementRef<typeof ScrollView>>(null);

  const [isShowButton, setIsShowButton] = useState<ScrollPosition>(
    CurrentScroll.TOP,
  );

  // Memoized function to check if type is chosen
  const isChooseType = useCallback(
    (cleaningType: string) => {
      if (!cleanTypeChoose) {
        return false;
      }
      return Boolean(cleanTypeChoose.type === cleaningType);
    },
    [cleanTypeChoose],
  );

  // Initialize clean type choose based on existing data
  useEffect(() => {
    if (dryCleaningChoose && dryCleaningChoose.length > 0) {
      let mainType = '';
      if (dryCleaningChoose[0]?.type) {
        mainType = dryCleaningChoose[0].type.split('_').slice(0, 2).join('_');
      }

      const existTypeChoose = find(dryDetailArr, { type: mainType });
      if (existTypeChoose) {
        setCleanTypeChoose(existTypeChoose);
      }
    }
  }, [dryDetailArr, dryCleaningChoose]);

  const _onChooseCleanType = useCallback(
    (data: any) => {
      if (cleanTypeChoose?.type === data?.type) {
        setCleanTypeChoose(null);
        return;
      }
      setCleanTypeChoose(data);
      setIsRenderDetail(false);
    },
    [cleanTypeChoose?.type, setIsRenderDetail],
  );

  // Memoized function to calculate amounts
  const amountDryObj = useMemo(() => {
    const amountDry: Record<string, number> = {};
    if (dryCleaningChoose) {
      forEach(dryCleaningChoose, (data) => {
        let mainType = '';
        if (data?.type) {
          mainType = data.type.split('_').slice(0, 2).join('_');
        }

        if (amountDry[mainType]) {
          amountDry[mainType] += data.quantity;
        } else {
          amountDry[mainType] = data.quantity;
        }
      });
    }
    return amountDry;
  }, [dryCleaningChoose]);

  const handleScrollToTop = useCallback(() => {
    setIsShowButton(CurrentScroll.TOP);
    scrollViewRef.current?.scrollTo({ y: 0, animated: true });
  }, []);

  const handleScrollToEnd = useCallback(() => {
    setIsShowButton(CurrentScroll.END);
    scrollViewRef.current?.scrollToEnd({ animated: true });
  }, []);

  const onScrollEndDrag = useCallback((event: any) => {
    const {
      contentOffset: { x },
    } = event.nativeEvent;

    if (x <= RANGE) {
      setIsShowButton(CurrentScroll.TOP);
    }

    const WIDTH = width - width * TEN_PERCENT - Spacing.SPACE_12 * 4 - RANGE;
    if (x >= WIDTH) {
      setIsShowButton(CurrentScroll.END);
    }
  }, []);

  if (!isDryCleaning) {
    return null;
  }

  return (
    <BlockView style={styles.animatedContainer}>
      <BlockView row>
        <ConditionView
          condition={isShowButton === CurrentScroll.END}
          viewTrue={
            <ButtonScroll
              positionLeft={0}
              callBack={handleScrollToTop}
              isRotate
            />
          }
        />

        <ScrollView
          horizontal
          style={styles.scrollContainer}
          showsHorizontalScrollIndicator={false}
          ref={scrollViewRef}
          onScrollEndDrag={onScrollEndDrag}
          scrollEventThrottle={16}>
          {dryDetailArr.map((data) => (
            <ImageItem
              key={data.type}
              amount={amountDryObj[data.type]}
              isChoose={isChooseType(data.type)}
              onPress={_onChooseCleanType}
              data={data}
            />
          ))}

          <BlockView width={width * TEN_PERCENT} />
        </ScrollView>

        <ConditionView
          condition={isShowButton === CurrentScroll.TOP}
          viewTrue={
            <ButtonScroll
              positionRight={0}
              callBack={handleScrollToEnd}
            />
          }
        />
      </BlockView>

      <DryCleaningDetailList
        updateDryClean={updateDryClean}
        setIsRenderDetail={setIsRenderDetail}
        isRenderDetail={isRenderDetail}
        dryCleaningChoose={dryCleaningChoose}
        cleanTypeChoose={cleanTypeChoose}
      />
    </BlockView>
  );
};
