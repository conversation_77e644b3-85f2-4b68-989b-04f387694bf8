import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  DeviceHelper,
  Spacing,
} from '@btaskee/design-system';

const { HEIGHT } = DeviceHelper.WINDOW;
export const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: Spacing.SPACE_16,
  },
  marginRightItem: {
    marginRight: Spacing.SPACE_16,
  },
  tabBarContainer: {
    paddingVertical: Spacing.SPACE_12,
    paddingHorizontal: Spacing.SPACE_16,
  },
  titleTxt: {
    color: ColorsV2.neutral500,
  },
  titleTxtActive: {
    color: ColorsV2.orange500,
  },
  badgeContainer: {
    width: Spacing.SPACE_16,
    height: Spacing.SPACE_16,
    backgroundColor: ColorsV2.orange500,
    borderRadius: BorderRadius.RADIUS_FULL,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Spacing.SPACE_04,
    marginTop: -Spacing.SPACE_04,
  },
  badgeTxt: {
    color: ColorsV2.neutralWhite,
    fontSize: 10,
  },
  tabBarItem: {
    flex: 1,
    paddingVertical: Spacing.SPACE_12,
  },
  tabBarItemActive: {
    borderBottomColor: ColorsV2.orange500,
    borderBottomWidth: 1,
  },
  containerContent: {
    paddingBottom: HEIGHT * 0.2,
  },
  noteText: {
    color: ColorsV2.neutral600,
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
    marginTop: Spacing.SPACE_16,
    marginBottom: Spacing.SPACE_16,
  },
});
