/**
 * Styles for the Choose<PERSON>ddress screen
 */
import { StyleSheet } from 'react-native';
import { ColorsV2, FontSizes, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorsV2.neutral50,
  },
  wrapFlatList: {
    backgroundColor: ColorsV2.neutralWhite,
    padding: Spacing.SPACE_16,
    flex: 1,
  },
  txtDescription: {
    color: ColorsV2.neutral700,
    fontSize: FontSizes.SIZE_14,
    marginBottom: Spacing.SPACE_08,
  },
  contentContainer: {
    paddingBottom: Spacing.SPACE_52,
  },
});
