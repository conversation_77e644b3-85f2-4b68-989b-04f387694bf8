import React from 'react';
import { BlockView, CText, TouchableOpacity } from '@btaskee/design-system';
import LottieView from 'lottie-react-native';

import { useI18n } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { NavigationProps } from '@navigation/type';

import { tickLottie } from '../../assets/lottie';
import { styles } from './styles';

export const PostTaskSuccess = ({
  navigation,
}: {
  navigation: NavigationProps<RouteName.PostTaskSuccess>;
}) => {
  const { t } = useI18n();

  const _followTask = () => {
    // back to home page
    navigation.popToTop();
    navigation.navigate('TabNavigator', { screen: 'Tab_Activity' });
  };

  const _goHome = () => {
    navigation.popToTop();
  };

  return (
    <BlockView
      inset={'bottom'}
      style={styles.container}>
      {/* Content */}
      <BlockView
        flex
        center>
        <LottieView
          source={tickLottie}
          style={styles.lottieAnimation}
          resizeMode="contain"
          autoPlay={true}
        />
        <BlockView style={styles.contentContainer}>
          <CText
            bold
            style={styles.modalTitleStyle}>
            {t('MODAL_POST_TASK_SUCCESS_TITLE')}
          </CText>
          <CText style={styles.txtContent}>
            {t('MODAL_POST_TASK_SUCCESS_CONTENT')}
          </CText>
        </BlockView>
      </BlockView>

      {/* Action */}
      <BlockView
        inset={'bottom'}
        style={styles.btnActionBox}>
        <TouchableOpacity
          testID="postTaskSuccessBtn"
          onPress={_followTask}
          style={styles.touchableFollowTask}>
          <CText
            bold
            style={styles.txtBtnFollowTask}>
            {t('MODAL_POST_TASK_SUCCESS_BTN_FOLLOW_TASK')}
          </CText>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={_goHome}
          style={styles.touchableGoHome}>
          <CText
            bold
            style={styles.txtBtnGoHome}>
            {t('MODAL_POST_TASK_SUCCESS_BTN_GO_HOME')}
          </CText>
        </TouchableOpacity>
      </BlockView>
    </BlockView>
  );
};
