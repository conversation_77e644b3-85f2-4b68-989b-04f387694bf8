/**
 * @Filename: screens/ChooseDateTime/index.tsx
 * @Description: Choose date and time screen for laundry service
 * @CreatedAt: 16/9/2020
 * @Author: DucAnh
 * @UpdatedAt: Current
 * @UpdatedBy: AI Assistant
 **/

import React, { useMemo } from 'react';
import {
  Alert,
  BlockView,
  DatePicker,
  DateTimeHelpers,
  type IDate,
  NotePostTask,
  PostTaskHelpers,
  PriceButton,
  PriceIncrease,
  ScrollView,
  SizedBox,
  Spacing,
  TimePicker,
  useSettingsStore,
} from '@btaskee/design-system';
import type { ConfigType } from 'dayjs';

import { useAppNavigation, useChangeData, useI18n } from '@hooks';
import { RouteName } from '@navigation/RouteName';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

/**
 * Choose date and time screen for laundry service
 * @returns React component for choosing date and time
 */
export const ChooseDateTime = () => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const { onChangeDateTime, onChangeCollectionDate } = useChangeData();
  const {
    address,
    date,
    duration,
    note,
    setNote,
    setIsApplyNoteForAllTask,
    isApplyNoteForAllTask,
    price,
    service,
    collectionDate,
  } = usePostTaskStore();
  const { settings } = useSettingsStore();

  const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);

  const onConfirmed = () => {
    // check time before 60min
    if (
      !PostTaskHelpers.validateDateTime(
        timezone,
        date as IDate,
        settings?.settingSystem?.minPostTaskTime,
      )
    ) {
      return Alert?.alert?.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('POSTTASK_STEP2_ERROR_TIME', {
          t: settings?.settingSystem?.minPostTaskTime,
        }),
        actions: [{ text: t('PT2_POPUP_ERROR_TIME_CLOSE') }],
      });
    }

    // check posting limit, ex 6AM - 10PM
    const postingLimits = service?.postingLimits;
    if (
      !PostTaskHelpers.checkTimeValidFromService(
        timezone,
        date as IDate,
        duration,
        postingLimits,
      )
    ) {
      const postingLimitsFormat = PostTaskHelpers.formatPostingLimits({
        timezone,
        postingLimits,
      });
      return Alert?.alert?.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('PT2_POPUP_ERROR_TIME_INVALID_CONTENT', {
          from: postingLimitsFormat.from,
          to: postingLimitsFormat.to,
        }),
        actions: [{ text: t('PT2_POPUP_ERROR_TIME_INVALID_CLOSE') }],
      });
    }

    // data ok
    onGotoStep4();
  };

  const onGotoStep4 = () => {
    navigation.navigate(RouteName.ConfirmAndPayment);
  };

  const chooseCollectionDate = useMemo(() => {
    return (
      <BlockView style={styles.boxCollectionDate}>
        <DatePicker
          title={t('SV_LR_SCR3_DATE_TIME_COLLECTION_TITLE')}
          value={collectionDate as ConfigType}
          onChange={onChangeCollectionDate}
          settingSystem={settings?.settingSystem}
          timezone={timezone}
        />
        <TimePicker
          testID="collectionDate"
          title={t('SV_LR_SCR3_DATE_TIME_COLLECTION_TIME')}
          value={collectionDate as ConfigType}
          onChange={onChangeCollectionDate}
          settingSystem={settings?.settingSystem}
          timezone={timezone}
        />
      </BlockView>
    );
  }, [collectionDate, timezone]);

  const shouldRenderChooseDateTime = useMemo(() => {
    return (
      <BlockView>
        <DatePicker
          noShowTitle
          value={date as ConfigType}
          onChange={onChangeDateTime}
          settingSystem={settings?.settingSystem}
          timezone={timezone}
          minDay={DateTimeHelpers.toDateTz({
            timezone,
            date: collectionDate as ConfigType,
          })
            .subtract(1, 'day')
            .endOf('day')}
        />
        <TimePicker
          value={date as ConfigType}
          onChange={onChangeDateTime}
          settingSystem={settings?.settingSystem}
          timezone={timezone}
        />
      </BlockView>
    );
  }, [date, onChangeDateTime, settings?.settingSystem, timezone]);

  return (
    <BlockView
      inset={['bottom']}
      style={styles.container}>
      <BlockView style={styles.content}>
        <ScrollView
          contentContainerStyle={styles.containerScroll}
          showsVerticalScrollIndicator={false}>
          {chooseCollectionDate}
          <SizedBox height={Spacing.SPACE_16} />
          {shouldRenderChooseDateTime}
          <BlockView style={styles.boxPrice}>
            <PriceIncrease
              price={price}
              containerStyle={{ marginHorizontal: 0 }}
            />

            <NotePostTask
              setNote={setNote}
              value={note}
              service={service}
              isApplyNoteForAllTask={isApplyNoteForAllTask}
              setNoteForAllTask={setIsApplyNoteForAllTask}
              containerStyle={{ marginTop: Spacing.SPACE_24 }}
              placeholder={t('SERVICE_NOTE_CONTENT')}
              title={t('LABEL_NOTE_FOR_TASKER')}
              description={t('TASK_NOTE_DESCRIPTION')}
            />
          </BlockView>
          <SizedBox style={styles.styleBottom} />
        </ScrollView>
      </BlockView>
      <PriceButton
        testID="btnNextStep3"
        onPress={onConfirmed}
        fromScreen={service?.name}
        pricePostTask={price}
      />
    </BlockView>
  );
};
