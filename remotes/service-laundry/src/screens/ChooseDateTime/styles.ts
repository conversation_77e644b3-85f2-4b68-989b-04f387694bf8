/**
 * Styles for the ChooseDateTime screen
 */
import { StyleSheet } from 'react-native';
import { ColorsV2, DeviceHelper, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorsV2.neutralWhite,
  },
  content: {
    flex: 1,
  },
  containerScroll: {
    paddingBottom: Spacing.SPACE_16,
  },
  boxCollectionDate: {
    marginTop: Spacing.SPACE_16,
  },
  boxChooseDateTime: {
    marginTop: Spacing.SPACE_24,
  },
  boxPrice: {
    paddingHorizontal: Spacing.SPACE_16,
  },
  styleBottom: {
    height: DeviceHelper.WINDOW.HEIGHT * 0.15,
  },
});
