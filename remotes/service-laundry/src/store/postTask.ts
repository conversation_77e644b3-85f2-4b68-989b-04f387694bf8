import { create } from 'zustand';

import type { ILaundryService } from '../types/laundry';

export interface PostTaskState {
  washing: ILaundryService | null;
  dryClean: ILaundryService | null;
  others: ILaundryService | null;
  setWashing: (washing: ILaundryService | null) => void;
  setDryClean: (dryClean: ILaundryService | null) => void;
  setOthers: (others: ILaundryService | null) => void;
}

export const usePostTaskStore = create<PostTaskState>((set) => ({
  washing: null,
  dryClean: null,
  others: null,
  setWashing: (washing) => set({ washing }),
  setDryClean: (dryClean) => set({ dryClean }),
  setOthers: (others) => set({ others }),
}));
