import React, { useRef, useState } from 'react';
import {
  BlockView,
  BottomModal,
  BottomModalHandle,
  ColorsV2,
  ConditionView,
  <PERSON><PERSON>t,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ontSizes,
  getTextWithLocale,
  Markdown,
  ProcessButton,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { useI18n } from '@hooks';

import { KEY_WORKING_PROCESS } from '../../lib/constants';
import { ContentWorkingProcess } from './ContentWorkingProcess';
import { styles } from './styles';
import {
  IMoreThenTwoProcessProps,
  IOneProcessProps,
  IWorkingProcess,
  IWorkingProcessProps,
  TabProps,
} from './types';

const KEY_WORKING_PROCESS_LAUNDRY = [
  KEY_WORKING_PROCESS.DRY_CLEANING,
  KEY_WORKING_PROCESS.TUMBLE_DRYING,
];

const PERCENT_HEIGHT = 0.6;
const TabComponents = ({
  title,
  onPress,
  style,
  isActive,
  isFirst,
}: TabProps) => {
  const textColor = isActive ? ColorsV2.neutralWhite : ColorsV2.neutral900;
  const backgroundColor = isActive ? ColorsV2.orange500 : ColorsV2.neutral50;
  const marginLeft = isFirst ? 0 : Spacing.SPACE_16;

  return (
    <TouchableOpacity
      onPress={onPress}
      style={[styles.containerBtn, { backgroundColor, marginLeft }, style]}>
      <Markdown
        text={title}
        textStyle={{ ...styles.txtMarkdown, color: textColor }}
      />
    </TouchableOpacity>
  );
};

const MoreThenTwoProcess = ({
  workingProcess,
  activeTab,
  handleChangeTab,
}: IMoreThenTwoProcessProps) => {
  const { t } = useI18n();

  const getTitle = () => {
    if (
      activeTab?.name &&
      KEY_WORKING_PROCESS_LAUNDRY.includes(
        activeTab.name as KEY_WORKING_PROCESS,
      )
    ) {
      return t('LAUNDRY_PROCESS_TITLE');
    }
    return '';
  };

  return (
    <BlockView>
      <BlockView>
        <ConditionView
          condition={!isEmpty(getTitle())}
          viewTrue={
            <CText
              center
              bold
              size={FontSizes.SIZE_20}
              margin={{ top: Spacing.SPACE_12 }}>
              {getTitle()}
            </CText>
          }
        />
        <BlockView
          row
          jBetween
          padding={Spacing.SPACE_16}
          margin={{ top: Spacing.SPACE_12 }}>
          {workingProcess?.slice(0, 2)?.map((item, index) => (
            <TabComponents
              key={item?.name}
              title={getTextWithLocale(item?.text)}
              onPress={handleChangeTab?.(item)}
              isActive={activeTab?.name === item?.name}
              isFirst={index === 0}
            />
          ))}
        </BlockView>
        <BlockView
          maxHeight={DeviceHelper.WINDOW.HEIGHT * PERCENT_HEIGHT}
          padding={{ horizontal: Spacing.SPACE_16 }}>
          <ContentWorkingProcess
            key={activeTab?.name}
            workToDo={activeTab?.workToDo || []}
          />
        </BlockView>
      </BlockView>
    </BlockView>
  );
};

const OneProcess = ({ workingProcess }: IOneProcessProps) => {
  if (!workingProcess) return null;
  return (
    <BlockView inset="bottom">
      <BlockView
        padding={{ horizontal: Spacing.SPACE_16 }}
        center>
        <Markdown
          text={getTextWithLocale(workingProcess?.text)}
          textStyle={styles.txtMarkdownTitle}
        />
      </BlockView>
      <BlockView
        maxHeight={DeviceHelper.WINDOW.HEIGHT * PERCENT_HEIGHT}
        padding={{ horizontal: Spacing.SPACE_16 }}>
        <ContentWorkingProcess
          key={workingProcess?.name}
          workToDo={workingProcess?.workToDo || []}
        />
      </BlockView>
    </BlockView>
  );
};

export const WorkingProcess: React.FC<IWorkingProcessProps> = ({
  workingProcess,
}) => {
  const bottomSheetModalRef = useRef<BottomModalHandle>(null);
  const [activeTab, setActiveTab] = useState(workingProcess?.[0]);

  const handleChangeTab = (tab: IWorkingProcess) => () => {
    setActiveTab(tab);
  };

  const handleOpenModalWorkingProcess = () => {
    bottomSheetModalRef.current?.present();
  };

  return (
    <>
      <ProcessButton onPress={handleOpenModalWorkingProcess} />
      <BottomModal
        modalRef={bottomSheetModalRef}
        enableDynamicSizing={true}
        enablePanDownToClose={true}
        animateOnMount={true}>
        <ConditionView
          condition={Number(workingProcess?.length) === 1}
          viewTrue={<OneProcess workingProcess={workingProcess?.[0]} />}
          viewFalse={
            <MoreThenTwoProcess
              workingProcess={workingProcess}
              activeTab={activeTab}
              handleChangeTab={handleChangeTab}
            />
          }
        />
      </BottomModal>
    </>
  );
};
