import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorsV2.neutralWhite,
    paddingHorizontal: Spacing.SPACE_16,
    paddingVertical: Spacing.SPACE_20,
  },
  titleContainer: {
    marginBottom: Spacing.SPACE_16,
  },
  title: {
    fontSize: FontSizes.SIZE_18,
    color: ColorsV2.neutral900,
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: ColorsV2.neutral100,
    borderRadius: BorderRadius.RADIUS_08,
    padding: Spacing.SPACE_04,
    marginBottom: Spacing.SPACE_16,
  },
  tab: {
    flex: 1,
    paddingVertical: Spacing.SPACE_12,
    borderRadius: BorderRadius.RADIUS_04,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: ColorsV2.neutralWhite,
  },
  inactiveTab: {
    backgroundColor: ColorsV2.transparent,
  },
  tabText: {
    fontSize: FontSizes.SIZE_14,
    color: ColorsV2.neutral500,
  },
  activeTabText: {
    color: ColorsV2.neutral900,
  },
  contentContainer: {
    flex: 1,
  },
});
