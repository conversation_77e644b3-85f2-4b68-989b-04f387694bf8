import { IObjectText } from '@btaskee/design-system';

export interface IWorkingProcess {
  name: string;
  text: IObjectText;
  workToDo: IObjectText[];
}

export interface IWorkingProcessProps {
  workingProcess: IWorkingProcess[];
}

export interface TabProps {
  title: string;
  onPress: () => void;
  style?: any;
  isActive: boolean;
  isFirst: boolean;
}

export interface IMoreThenTwoProcessProps {
  workingProcess: IWorkingProcess[];
  activeTab: IWorkingProcess | undefined;
  handleChangeTab: (tab: IWorkingProcess) => () => void;
}

export interface IOneProcessProps {
  workingProcess: IWorkingProcess | undefined;
}
