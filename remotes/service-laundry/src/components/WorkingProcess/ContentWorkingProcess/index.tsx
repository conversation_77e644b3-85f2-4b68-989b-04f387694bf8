import React from 'react';
import {
  <PERSON>View,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  getTextWithLocale,
  Markdown,
  ScrollView,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { IWorkingProcess } from '../types';
import { styles } from './styles';

const RATIO_HEIGHT = 1.5;

interface IContentWorkingProcessProps {
  workToDo?: IWorkingProcess['workToDo'];
}

export const ContentWorkingProcess: React.FC<IContentWorkingProcessProps> = ({
  workToDo,
}) => {
  if (isEmpty(workToDo)) {
    return null;
  }

  return (
    <BlockView
      maxHeight={Math.round(DeviceHelper.WINDOW.HEIGHT / RATIO_HEIGHT)}>
      <ScrollView
        bounces={false}
        showsVerticalScrollIndicator={false}>
        {workToDo?.map((item) => (
          <BlockView key={getTextWithLocale(item)}>
            <Markdown
              text={getTextWithLocale(item)}
              textStyle={styles.txtMarkdown}
            />
          </BlockView>
        ))}
      </ScrollView>
    </BlockView>
  );
};
