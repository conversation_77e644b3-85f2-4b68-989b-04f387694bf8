import React, { useCallback, useMemo, useState } from 'react';
import type { TextStyle, ViewStyle } from 'react-native';
import {
  BlockView,
  ColorsV2,
  CText,
  Icon,
  TouchableOpacity,
} from '@btaskee/design-system';

import { styles } from './styles';

interface NumberSpinnerProps {
  /** Title displayed next to the spinner */
  title?: string;
  /** Custom styles for the title text */
  textTitleStyle?: TextStyle;
  /** External flag to disable the plus button */
  disableBtnPlus?: boolean;
  /** Callback function when value changes */
  onValueChange: (value: number) => void;
  /** Maximum allowed value */
  maxValue?: number;
  /** Current value */
  value?: number;
  /** Test ID for automation testing */
  testID: string;
  /** Custom styles for the container */
  containerStyle?: ViewStyle;
  /** Custom styles for the spinner container */
  spinnerContainer?: ViewStyle;
  /** Minimum allowed value (default: 0) */
  minValue?: number;
}

/**
 * NumberSpinner component for incrementing/decrementing values with proper minValue support
 * Follows bTaskee design system patterns and includes proper validation
 */
export const NumberSpinner: React.FC<NumberSpinnerProps> = React.memo(
  ({
    title,
    textTitleStyle,
    disableBtnPlus = false,
    onValueChange,
    maxValue,
    value = 0,
    testID,
    containerStyle,
    spinnerContainer,
    minValue = 0,
  }) => {
    // Initialize state - allow value to be 0 or default value
    const [amount, setAmount] = useState(value);

    // Computed disable states with proper minValue logic
    const isDecrementDisabled = useMemo(() => {
      return amount <= minValue;
    }, [amount, minValue]);

    const isIncrementDisabled = useMemo(() => {
      return disableBtnPlus || (maxValue != null && amount >= maxValue);
    }, [disableBtnPlus, maxValue, amount]);

    // Icon colors based on disabled states
    const minusIconColor: string = isDecrementDisabled
      ? ColorsV2.neutral200
      : ColorsV2.neutralWhite;

    const plusIconColor: string = isIncrementDisabled
      ? ColorsV2.neutral200
      : ColorsV2.neutralWhite;

    // Button styles based on disabled states
    const decrementButtonStyle = isDecrementDisabled
      ? styles.disableButtonStyle
      : styles.activeButtonStyle;

    const incrementButtonStyle = isIncrementDisabled
      ? styles.disableButtonStyle
      : styles.activeButtonStyle;

    /**
     * Handle decrement with proper minValue validation
     */
    const handleDecrement = useCallback(() => {
      if (amount <= minValue) return;

      const newAmount = Math.max(amount - 1, minValue);
      setAmount(newAmount);
      onValueChange?.(newAmount);
    }, [amount, minValue, onValueChange]);

    /**
     * Handle increment with new logic:
     * - If value >= minValue, add 1
     * - If value < minValue, set to minValue
     */
    const handleIncrement = useCallback(() => {
      if (maxValue != null && amount >= maxValue) return;

      let newAmount;
      if (amount >= minValue) {
        newAmount = amount + 1;
      } else {
        newAmount = minValue;
      }

      setAmount(newAmount);
      onValueChange?.(newAmount);
    }, [amount, maxValue, minValue, onValueChange]);

    /**
     * Title section with optional styling
     */
    const TitleSection = useMemo(() => {
      if (!title) return null;

      return (
        <BlockView
          flex
          style={styles.boxTitle}>
          <CText
            bold
            style={[styles.txtTitle, textTitleStyle]}>
            {title}
          </CText>
        </BlockView>
      );
    }, [title, textTitleStyle]);

    /**
     * Full counter with minus, amount, and plus buttons
     */
    const FullCounter = useCallback(
      () => (
        <BlockView
          flex
          center
          style={[styles.spinnerContainer, spinnerContainer]}
          jBetween
          row>
          <TouchableOpacity
            onPress={handleDecrement}
            testID={`btnDecrement_${testID}`}
            disabled={isDecrementDisabled}
            style={[styles.boxBtnAmount, decrementButtonStyle]}>
            <Icon
              testID="btnMinus"
              name="icSubtractFill"
              color={minusIconColor}
            />
          </TouchableOpacity>

          <TouchableOpacity style={styles.amountDisplayContainer}>
            <CText
              testID="txtAmount"
              bold
              style={styles.txtAmount}>
              {amount.toString()}
            </CText>
          </TouchableOpacity>

          <TouchableOpacity
            testID={`btnIncrement_${testID}`}
            disabled={isIncrementDisabled}
            onPress={handleIncrement}
            style={[styles.boxBtnAmount, incrementButtonStyle]}>
            <Icon
              testID="btnPlus"
              name="icPlusFill"
              color={plusIconColor}
            />
          </TouchableOpacity>
        </BlockView>
      ),
      [
        handleDecrement,
        handleIncrement,
        testID,
        isDecrementDisabled,
        isIncrementDisabled,
        decrementButtonStyle,
        incrementButtonStyle,
        minusIconColor,
        plusIconColor,
        amount,
        spinnerContainer,
      ],
    );

    return (
      <BlockView
        row
        horizontal
        style={[styles.container, containerStyle]}>
        {TitleSection}
        <FullCounter />
      </BlockView>
    );
  },
);

NumberSpinner.displayName = 'NumberSpinner';
