import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    borderRadius: BorderRadius.RADIUS_08,
    borderWidth: 1,
    marginTop: Spacing.SPACE_16,
    borderColor: ColorsV2.neutral100,
  },

  txtTitle: {
    lineHeight: 20,
    color: ColorsV2.neutral900,
    fontSize: FontSizes.SIZE_14,
    marginRight: Spacing.SPACE_16,
  },

  boxBtnAmount: {
    width: 24,
    height: 24,
    borderRadius: BorderRadius.RADIUS_08,
    alignItems: 'center',
    justifyContent: 'center',
  },

  activeButtonStyle: {
    backgroundColor: ColorsV2.orange500,
  },

  disableButtonStyle: {
    borderWidth: 1,
    backgroundColor: ColorsV2.neutralWhite,
    borderColor: ColorsV2.neutral100,
  },

  amountDisplayContainer: {
    width: 24,
    height: 24,
    borderRadius: BorderRadius.RADIUS_08,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: ColorsV2.neutralWhite,
    borderColor: ColorsV2.neutral100,
    borderWidth: 1,
  },

  spinnerContainer: {
    padding: Spacing.SPACE_08,
    minHeight: 40,
    backgroundColor: ColorsV2.neutral50,
  },

  disabledPlusButton: {
    opacity: 0.5,
  },

  txtAmount: {
    color: ColorsV2.neutral900,
    fontSize: FontSizes.SIZE_14,
  },

  boxTitle: {
    flexDirection: 'column',
  },

  txtMinPrice: {
    color: ColorsV2.neutral500,
    marginTop: Spacing.SPACE_04,
    fontSize: FontSizes.SIZE_12,
  },
});
