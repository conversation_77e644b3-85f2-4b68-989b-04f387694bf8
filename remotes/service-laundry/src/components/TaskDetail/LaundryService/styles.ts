import { StyleSheet } from 'react-native';
import { ColorsV2, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  groupDryClean: {
    paddingTop: Spacing.SPACE_08,
  },
  txtTitle: {
    color: ColorsV2.orange500,
  },
  wrapContent: {
    paddingVertical: Spacing.SPACE_08,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  txtQuantity: {
    color: ColorsV2.neutral300,
    width: '35%',
    paddingRight: Spacing.SPACE_08,
  },
  txtDryClean: {
    textAlign: 'right',
    color: ColorsV2.neutral900,
  },
  txtAmountDryClean: {
    color: ColorsV2.orange500,
  },
  rightContent: {
    width: '65%',
  },
});
