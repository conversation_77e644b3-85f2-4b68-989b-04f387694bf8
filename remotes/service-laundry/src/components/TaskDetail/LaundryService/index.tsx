/**
 * @Filename: TaskDetail/LaundryService/index.tsx
 * @Description: Displays laundry service details including washing, dry cleaning, and other services
 * @CreatedAt: 18/9/2020
 * @Author: DucAnh
 * @UpdatedAt: Current
 * @UpdatedBy: AI Assistant
 **/

import React from 'react';
import {
  BlockView,
  ConditionView,
  CText,
  getTextWithLocale,
} from '@btaskee/design-system';
import type { LaundryOtherItem, LaundryWashingItem } from '@types';

import { useI18n } from '@hooks';
import { usePostTaskStore } from '@stores';

import { styles } from './styles';

/**
 * Displays laundry service details including washing, dry cleaning and other services
 * @returns React component displaying laundry service details
 */
export const LaundryService = (): React.ReactElement => {
  const { t } = useI18n();

  const { washing, dryClean, others, otherText } = usePostTaskStore();

  return (
    <BlockView>
      {washing?.dataV2 && washing.dataV2.length > 0 && (
        <BlockView style={styles.groupDryClean}>
          <CText
            bold
            style={styles.txtTitle}>
            {getTextWithLocale(washing.text)}
          </CText>

          <BlockView style={styles.wrapContent}>
            <CText style={styles.txtQuantity}>{t('CLOTHES_TYPE')}</CText>
            <BlockView style={styles.rightContent}>
              {washing.dataV2.map((item: LaundryWashingItem, index: number) => (
                <CText
                  style={styles.txtDryClean}
                  key={index}
                  numberOfLines={1}>
                  {getTextWithLocale(item?.text)}
                  <CText
                    bold
                    style={styles.txtAmountDryClean}>
                    {' '}
                    {t('DRY_CLEAN_AMOUNT', { t: item.quantity })}
                  </CText>
                </CText>
              ))}
            </BlockView>
          </BlockView>
        </BlockView>
      )}

      {dryClean?.data && dryClean.data.length > 0 && (
        <BlockView style={styles.groupDryClean}>
          <CText
            bold
            style={styles.txtTitle}>
            {getTextWithLocale(dryClean.text)}
          </CText>

          <BlockView style={styles.wrapContent}>
            <CText style={styles.txtQuantity}>{t('CLOTHES_TYPE')}</CText>
            <BlockView style={styles.rightContent}>
              {dryClean.data.map((item: LaundryWashingItem, index) => (
                <CText
                  style={styles.txtDryClean}
                  key={index}
                  numberOfLines={2}>
                  {getTextWithLocale(item?.text)}
                  <CText
                    bold
                    style={styles.txtAmountDryClean}>
                    {' '}
                    {t('DRY_CLEAN_AMOUNT', { t: item.quantity })}
                  </CText>
                </CText>
              ))}
            </BlockView>
          </BlockView>
        </BlockView>
      )}

      {others?.dataV2 && others.dataV2.length > 0 && (
        <BlockView style={styles.groupDryClean}>
          <CText
            bold
            style={styles.txtTitle}>
            {t('OTHER_SERVICE')}
          </CText>

          <BlockView style={styles.wrapContent}>
            <CText style={styles.txtQuantity}>{t('CLOTHES_TYPE')}</CText>
            <BlockView style={styles.rightContent}>
              {others.dataV2.map((item: LaundryOtherItem, index) => (
                <CText
                  style={styles.txtDryClean}
                  key={index}
                  numberOfLines={2}>
                  {getTextWithLocale(item?.text)}
                  <CText
                    bold
                    style={styles.txtAmountDryClean}>
                    {' '}
                    {t('DRY_CLEAN_AMOUNT', { t: item.quantity })}
                  </CText>
                </CText>
              ))}
            </BlockView>
          </BlockView>
        </BlockView>
      )}

      <ConditionView
        condition={Boolean(otherText)}
        viewTrue={
          <BlockView style={styles.wrapContent}>
            <CText style={styles.txtQuantity}>{t('OTHER')}</CText>
            <BlockView
              right
              style={{ width: '65%' }}>
              <CText style={styles.txtDryClean}>{otherText}</CText>
            </BlockView>
          </BlockView>
        }
      />
    </BlockView>
  );
};
