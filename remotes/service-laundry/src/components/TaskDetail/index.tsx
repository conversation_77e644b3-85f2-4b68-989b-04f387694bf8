/**
 * @Filename: TaskDetail/index.tsx
 * @Description: TaskDetail component for service-laundry
 * @CreatedAt: 18/9/2020
 * @Author: Du<PERSON><PERSON><PERSON>
 * @UpdatedAt: Current
 * @UpdatedBy: AI Assistant
 **/

import React from 'react';
import { BlockView, Card, CText } from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { LaundryService } from './LaundryService';
import { styles } from './styles';

/**
 * TaskDetail component displays detailed information about a laundry task
 * including service details, working time, and other task-specific information
 * @returns React component displaying task details
 */
export const TaskDetail = (): React.ReactElement => {
  const { t } = useI18n();

  return (
    <BlockView>
      <BlockView style={styles.headerContainer}>
        <CText
          bold
          style={styles.headerText}>
          {t('TASK_INFO')}
        </CText>
      </BlockView>
      <Card>
        <BlockView style={styles.detailContainer}>
          <CText
            bold
            style={styles.detailTitle}>
            {t('TASK_DETAIL')}
          </CText>
          <LaundryService />
        </BlockView>
      </Card>
    </BlockView>
  );
};
