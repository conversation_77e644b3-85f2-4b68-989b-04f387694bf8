#!/bin/bash
source scripts/script-helpers.sh

# Đường dẫn tới tệp index.js
file_path="index.js"
folder_node_name="btaskee-test-asker"
ssh_repo_node="**************:btaskee/btaskee-mobile/btaskee-test-asker.git"

folder_go_name="go-docker"
ssh_repo_go="**************:btaskee-testing/go-docker.git"

folder_react_native_name="super-app-workspace"
env_folder=env-docker

# Giá trị mặc định của các cổng
PORT_NODE=3344
PORT_GO=8080
PORT_METRO=8081
PORT_DB=2707
NUMBER_CONTAINER=1
DB_NAME="ci-db"
FEATURE_GO_NAME=""
COUNTRY='vn'
MAX_RE_TRY=3

pull_repo_node() {
  cd ../../..
  # Check if repository exists at the same level as super-app-workspace
  if [ ! -d "$folder_node_name" ]; then
    log_color $YELLOW "Repository $folder_node_name not found at $(pwd)/$folder_node_name"
    log_color $YELLOW "Attempting to clone..."
    git clone --branch update-test-case-asker --single-branch $ssh_repo_node
  else
    log_color $GREEN "FOUND EXISTING REPO NODE -> $(pwd)/$folder_node_name"
  fi

  cd $folder_node_name
  # Only do git operations if it's a git repository
  if [ -d ".git" ]; then
    git reset --hard
    log_color $GREEN "PULL REPO NODE -> $(pwd)"
    output=$(git pull)
    echo $output
  else
    log_color $YELLOW "Directory exists but is not a git repository: $(pwd)"
  fi

  # Check if docker-build.sh exists before running it
  if [ -f "./docker-build.sh" ]; then
    COUNTRY=$COUNTRY ./docker-build.sh
  else
    log_color $YELLOW "docker-build.sh not found in $(pwd)"
  fi

  cd ..
  cd $folder_react_native_name/apps/host
}

pull_repo_go() {
  cd ../../..
  # Check if repository exists at the same level as super-app-workspace
  if [ ! -d "$folder_go_name" ]; then
    log_color $RED "ERROR: go-docker repository not found at $(pwd)/$folder_go_name"
    log_color $YELLOW "Please ensure the go-docker repository exists at $(pwd)/$folder_go_name"
    log_color $YELLOW "You can clone it with: git clone $ssh_repo_go"
    exit 1
  else
    log_color $GREEN "FOUND EXISTING REPO GO -> $(pwd)/$folder_go_name"
  fi

  cd $folder_go_name
  # Only do git operations if it's a git repository
  if [ -d ".git" ]; then
    log_color $GREEN "USING EXISTING REPO GO -> $(pwd)"
    # Uncomment the next lines if you want to update the repository
    # git reset --hard
    # git pull
  else
    log_color $YELLOW "Directory exists but is not a git repository: $(pwd)"
  fi

  cd ..
  cd $folder_react_native_name/apps/host
}

pull_image_go() {
  log_color $YELLOW "PULL IMAGE GO -> RUNNING..."
  cd $root_dir
  cd ../../../$folder_go_name
  number_re_try_pull_image_go=0
  is_pull_image_go_success=false

  while [ "$is_pull_image_go_success" == false ]; do
    if [ $number_re_try_pull_image_go -eq $MAX_RE_TRY ]; then
      log_color $RED "PULL IMAGE GO -> FAILED"
      exit 1
    fi
    FEATURE_NAME=$FEATURE_GO_NAME ./docker-build.sh pull
    exit_code_pull_image_go=$? # Lưu lại kết quả lệnh ở trên

    # Kiểm tra kết quả của lệnh ./docker-build.sh pull
    if [ $exit_code_pull_image_go -eq 0 ]; then
      is_pull_image_go_success=true
    else
      ((number_re_try_pull_image_go++))
      log_color $YELLOW "PULL IMAGE GO -> TRYING AGAIN [$number_re_try_pull_image_go]..."
      sleep 5 # Đợi 5 giây trước khi thử lại
    fi
  done
  log_color $GREEN "PULL IMAGE GO -> SUCCESS"
}

run() {
  root_dir=$(pwd)
  echo "ROOT_DIR: $root_dir"

  # Lấy số lượng vòng lặp từ tham số đầu vào
  iterations=$NUMBER_CONTAINER

  open_docker
  pnpm ci:multi:docker:end

  pull_repo_node
  log_color $YELLOW "Using existing go-docker repository..."
  pull_repo_go

  #Kết thúc tất cả docker đang chạy trước đó
  pull_image_go

  if [ ! -d "$root_dir/$env_folder" ]; then
    mkdir "$root_dir/$env_folder"
  fi

  # Vòng lặp dựa vào số lần nhập vào
  for ((i = 1; i <= iterations; i++)); do
    log_color $YELLOW "START CONTAINER $i -> RUNNING..."
    # Tên file .env
    ENV_FILE="$root_dir/$env_folder/$i"
    if [ ! -f "$ENV_FILE" ]; then
      touch "$ENV_FILE"
    fi
    NAME_DOCKER_COMPOSE="docker$i"
    # Tạo nội dung file .env
    echo "PORT_NODE=$PORT_NODE" >$ENV_FILE
    echo "PORT_GO=$PORT_GO" >>$ENV_FILE
    echo "PORT_METRO=$PORT_METRO" >>$ENV_FILE
    echo "NAME_GO=go_testing_$i" >>$ENV_FILE
    echo "PORT_DB=$PORT_DB" >>$ENV_FILE

    # Tăng giá trị của các cổng
    PORT_NODE=$((PORT_NODE + 1))
    PORT_GO=$((PORT_GO + 1))
    PORT_METRO=$((PORT_METRO + 1))
    PORT_DB=$((PORT_DB + 1))
    # Biến để kiểm tra lệnh docker compose up thành công
    success=false
    number_re_try=0

    # Vòng lặp để chạy lại nếu lệnh docker compose up thất bại
    while [ "$success" == false ]; do
      if [ $number_re_try -eq $MAX_RE_TRY ]; then
        log_color $RED "START CONTAINER $i -> FAILED"
        exit 1
      fi

      cd $root_dir
      cd ../../../$folder_go_name
      PORT_GO_PREV=$((PORT_GO - 1))
      PORT_DB_PREV=$((PORT_DB - 1))
      if [ "$FEATURE_GO_NAME" == "" ]; then
        PROJECT_NAME=$NAME_DOCKER_COMPOSE PORT_GO=$PORT_GO_PREV MONGO_DB_NAME=$DB_NAME PORT_DB=$PORT_DB_PREV COUNTRY=$COUNTRY ./docker-build.sh
        local exit_code_go=$? #Lưu lại kết quả lệnh ở trên
      else
        FEATURE_NAME=$FEATURE_GO_NAME PROJECT_NAME=$NAME_DOCKER_COMPOSE PORT_GO=$PORT_GO_PREV MONGO_DB_NAME=$DB_NAME PORT_DB=$PORT_DB_PREV COUNTRY=$COUNTRY ./docker-build.sh
        local exit_code_go=$? #Lưu lại kết quả lệnh ở trên
      fi

      cd $root_dir
      cd ./scripts
      # Chạy lệnh docker compose up với file .env và tên project
      docker compose -p $NAME_DOCKER_COMPOSE --env-file $ENV_FILE up -d --build --force-recreate
      local exit_code=$? #Lưu lại kết quả lệnh ở trên

      # Kiểm tra kết quả của lệnh docker compose up và ./docker-build.sh của go
      if [ $exit_code -eq 0 ] && [ $exit_code_go -eq 0 ]; then
        success=true
      else
        ((number_re_try++))
        log_color $RED "START CONTAINER $i -> TRYING AGAIN [$number_re_try]..."
        sleep 5 # Đợi 5 giây trước khi thử lại
      fi
    done
    log_color $GREEN "START CONTAINER $i PORT_GO=$PORT_GO MONGO_DB_NAME=$DB_NAME -> SUCCESS"
  done
  remove_images_dangling
}

for arg in "$@"; do
  case $arg in
  --numberContainer=*)
    if [[ -n "${arg#*=}" ]]; then
      NUMBER_CONTAINER="${arg#*=}"
      log_color $YELLOW "--numberContainer=$NUMBER_CONTAINER"
    fi
    ;;
  --portNode=*)
    if [[ -n "${arg#*=}" ]]; then
      PORT_NODE="${arg#*=}"
      log_color $YELLOW "--portNode=$PORT_NODE"
    fi
    ;;
  --portGo=*)
    if [[ -n "${arg#*=}" ]]; then
      PORT_GO="${arg#*=}"
      log_color $YELLOW "--portGo=$PORT_GO"
    fi
    ;;
  --portMetro=*)
    if [[ -n "${arg#*=}" ]]; then
      PORT_METRO="${arg#*=}"
      log_color $YELLOW "--portMetro=$PORT_METRO"
    fi
    ;;
  --portDB=*)
    if [[ -n "${arg#*=}" ]]; then
      PORT_DB="${arg#*=}"
      log_color $YELLOW "--portDB=$PORT_DB"
    fi
    ;;
  --featureGoName=*)
    if [[ -n "${arg#*=}" ]]; then
      FEATURE_GO_NAME="${arg#*=}"
      log_color $YELLOW "--featureGoName=$FEATURE_GO_NAME"
    fi
    ;;
  --country=*)
    if [[ -n "${arg#*=}" ]]; then
      COUNTRY="${arg#*=}"
      log_color $YELLOW "--country=$COUNTRY"
    fi
    ;;
  esac
done

run
